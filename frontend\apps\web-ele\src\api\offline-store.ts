import { requestClient } from '#/api/request';

export interface OfflineStore {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface OfflineStoreSearchParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: string;
}

export interface OfflineStoreCreateData {
  name: string;
  status: number;
}

export interface OfflineStoreUpdateData {
  name: string;
  status: number;
}

// 获取线下店铺列表
export async function fetchOfflineStoreList(params: OfflineStoreSearchParams) {
  return requestClient.get<{
    data: OfflineStore[];
    total: number;
    page: number;
    pageSize: number;
  }>('/offline-stores', {
    params,
    responseReturn: 'body'
  });
}

// 创建线下店铺
export async function addOfflineStore(data: OfflineStoreCreateData) {
  return requestClient.post<{
    data: OfflineStore;
    message: string;
  }>('/offline-stores', data);
}

// 更新线下店铺
export async function updateOfflineStore(id: number, data: OfflineStoreUpdateData) {
  return requestClient.put<{
    message: string;
  }>(`/offline-stores/${id}`, data);
}

// 删除线下店铺
export async function deleteOfflineStore(id: number) {
  return requestClient.delete<{
    message: string;
  }>(`/offline-stores/${id}`);
}

// 批量删除线下店铺
export async function batchDeleteOfflineStore(ids: number[]) {
  return requestClient.post<{
    message: string;
  }>('/offline-stores/batch-delete', { ids });
}

// 更新线下店铺状态
export async function updateOfflineStoreStatus(id: number, status: boolean) {
  return requestClient.put<{
    message: string;
  }>(`/offline-stores/${id}/status`, { status: status ? 1 : 0 });
}
