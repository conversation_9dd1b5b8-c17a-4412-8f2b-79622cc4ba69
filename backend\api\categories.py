from flask import Blueprint, request, jsonify
from .utils import get_db_connection, format_response
from .auth import token_required, check_permission

categories_bp = Blueprint('categories', __name__, url_prefix='/api')

@categories_bp.route('/income-categories', methods=['GET'])
@token_required
def get_income_categories():
    """获取收入分类列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询启用的收入分类
        cursor.execute('''
            SELECT id, name, status, created_at, updated_at
            FROM income_categories 
            WHERE isdelete = 0 AND status = 1
            ORDER BY id ASC
        ''')
        
        categories = []
        for row in cursor.fetchall():
            categories.append({
                'id': row[0],
                'name': row[1],
                'status': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        # 格式化时间字段
        from utils.datetime_utils import format_datetime_list
        categories = format_datetime_list(categories, ['created_at', 'updated_at'])

        return format_response(
            success=True,
            data=categories,
            message="获取收入分类成功"
        )
        
    except Exception as e:
        print(f"获取收入分类错误: {str(e)}")
        return format_response(success=False, message="获取收入分类失败")
    finally:
        conn.close()

@categories_bp.route('/expenditure-categories', methods=['GET'])
@token_required
def get_expenditure_categories():
    """获取支出分类列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询启用的支出分类
        cursor.execute('''
            SELECT id, name, status, created_at, updated_at
            FROM expenditure_categories 
            WHERE isdelete = 0 AND status = 1
            ORDER BY id ASC
        ''')
        
        categories = []
        for row in cursor.fetchall():
            categories.append({
                'id': row[0],
                'name': row[1],
                'status': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        # 格式化时间字段
        from utils.datetime_utils import format_datetime_list
        categories = format_datetime_list(categories, ['created_at', 'updated_at'])

        return format_response(
            success=True,
            data=categories,
            message="获取支出分类成功"
        )
        
    except Exception as e:
        print(f"获取支出分类错误: {str(e)}")
        return format_response(success=False, message="获取支出分类失败")
    finally:
        conn.close()