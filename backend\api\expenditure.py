#!/usr/bin/env python3
"""支出分类管理API"""

from flask import Blueprint, request, jsonify
import sqlite3
from datetime import datetime
from .auth import token_required, check_permission
from .utils import get_db_connection, format_response, validate_required_fields

expenditure_bp = Blueprint('expenditure', __name__, url_prefix='/api')

@expenditure_bp.route('/expenditure-categories', methods=['GET'])
@token_required
@check_permission('MENU_EXPENDITURE')
def get_expenditure_categories():
    """获取支出分类列表"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        name = request.args.get('name', '').strip()
        status = request.args.get('status', '').strip()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_conditions = ["isdelete = 0"]
        params = []
        
        if name:
            where_conditions.append("name LIKE %s")
            params.append(f"%{name}%")
            
        if status:
            where_conditions.append("status = %s")
            params.append(int(status))
        
        where_clause = " AND ".join(where_conditions)
        
        # 获取总数
        count_sql = f"SELECT COUNT(*) FROM expenditure_categories WHERE {where_clause}"
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, name, status, created_at, updated_at 
            FROM expenditure_categories 
            WHERE {where_clause}
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """
        cursor.execute(data_sql, params + [page_size, offset])
        
        categories = []
        for row in cursor.fetchall():
            categories.append({
                'id': row[0],
                'name': row[1],
                'status': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        # 格式化时间字段
        from utils.datetime_utils import format_datetime_list
        categories = format_datetime_list(categories, ['created_at', 'updated_at'])

        conn.close()

        return format_response(
            data=categories,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        return format_response(success=False, message=f"获取支出分类列表失败: {str(e)}")

@expenditure_bp.route('/expenditure-categories', methods=['POST'])
@token_required
@check_permission('EXPENDITURE_ADD')
def create_expenditure_category():
    """创建支出分类"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error
        
        name = data['name'].strip()
        status = data.get('status', 1)
        
        # 检查名称是否已存在
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id FROM expenditure_categories WHERE name = %s AND isdelete = 0", (name,))
        if cursor.fetchone():
            conn.close()
            return format_response(success=False, message="支出分类名称已存在")
        
        # 插入新记录
        now = datetime.now().isoformat()
        cursor.execute("""
            INSERT INTO expenditure_categories (name, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s)
        """, (name, status, now, now))
        
        category_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return format_response(
            data={'id': category_id, 'name': name, 'status': status},
            message="支出分类创建成功"
        )
        
    except Exception as e:
        return format_response(success=False, message=f"创建支出分类失败: {str(e)}")

@expenditure_bp.route('/expenditure-categories/<int:category_id>', methods=['PUT'])
@token_required
@check_permission('EXPENDITURE_EDIT')
def update_expenditure_category(category_id):
    """更新支出分类"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error
        
        name = data['name'].strip()
        status = data.get('status', 1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM expenditure_categories WHERE id = %s AND isdelete = 0", (category_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="支出分类不存在")
        
        # 检查名称是否已被其他记录使用
        cursor.execute("SELECT id FROM expenditure_categories WHERE name = %s AND id != %s AND isdelete = 0", (name, category_id))
        if cursor.fetchone():
            conn.close()
            return format_response(success=False, message="支出分类名称已存在")
        
        # 更新记录
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE expenditure_categories 
            SET name = %s, status = %s, updated_at = %s
            WHERE id = %s
        """, (name, status, now, category_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="支出分类更新成功")
        
    except Exception as e:
        return format_response(success=False, message=f"更新支出分类失败: {str(e)}")

@expenditure_bp.route('/expenditure-categories/<int:category_id>', methods=['DELETE'])
@token_required
@check_permission('EXPENDITURE_DELETE')
def delete_expenditure_category(category_id):
    """删除支出分类"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM expenditure_categories WHERE id = %s AND isdelete = 0", (category_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="支出分类不存在")
        
        # 软删除
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE expenditure_categories 
            SET isdelete = 1, updated_at = %s
            WHERE id = %s
        """, (now, category_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="支出分类删除成功")
        
    except Exception as e:
        return format_response(success=False, message=f"删除支出分类失败: {str(e)}")

@expenditure_bp.route('/expenditure-categories/batch-delete', methods=['POST'])
@token_required
@check_permission('EXPENDITURE_BATCH_DELETE')
def batch_delete_expenditure_categories():
    """批量删除支出分类"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return format_response(success=False, message="请选择要删除的支出分类")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 软删除
        now = datetime.now().isoformat()
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f"""
            UPDATE expenditure_categories 
            SET isdelete = 1, updated_at = %s
            WHERE id IN ({placeholders}) AND isdelete = 0
        """, [now] + ids)
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return format_response(message=f"成功删除 {affected_rows} 个支出分类")
        
    except Exception as e:
        return format_response(success=False, message=f"批量删除支出分类失败: {str(e)}")

@expenditure_bp.route('/expenditure-categories/<int:category_id>/status', methods=['PUT'])
@token_required
@check_permission('EXPENDITURE_STATUS')
def update_expenditure_category_status(category_id):
    """更新支出分类状态"""
    try:
        data = request.get_json()
        status = data.get('status')
        
        if status is None:
            return format_response(success=False, message="状态参数不能为空")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM expenditure_categories WHERE id = %s AND isdelete = 0", (category_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="支出分类不存在")
        
        # 更新状态
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE expenditure_categories 
            SET status = %s, updated_at = %s
            WHERE id = %s
        """, (int(status), now, category_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="支出分类状态更新成功")
        
    except Exception as e:
        return format_response(success=False, message=f"更新支出分类状态失败: {str(e)}")
