from flask import Blueprint, g, request
from utils.jwt_utils import JWTUtils
from utils.response import ResponseUtils
from functools import wraps
from models.permission import Permission

menu_bp = Blueprint('menu', __name__, url_prefix='/api/menu')

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return ResponseUtils.unauthorized("令牌格式错误")
        
        if not token:
            return ResponseUtils.unauthorized("缺少访问令牌")
        
        # 验证令牌
        payload = JWTUtils.verify_access_token(token)
        if not payload:
            return ResponseUtils.unauthorized("无效的访问令牌")
        
        # 将用户信息存储到g对象中
        g.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

@menu_bp.route('/all', methods=['GET'])
@token_required
def get_all_menus():
    """获取用户所有菜单接口"""
    try:
        user_id = g.current_user['user_id']

        # 获取用户的菜单权限
        permission_model = Permission()
        user_permissions = permission_model.get_user_menu_permissions(user_id)

        # 将权限转换为权限码集合
        permission_codes = {perm['code'] for perm in user_permissions}

        # 构建菜单结构
        menus = []

        # 首页菜单 - 始终显示，作为默认页面
        menus.append({
            "id": 1,
            "name": "Home",
            "path": "/home",
            "component": "/home/<USER>",
            "meta": {
                "icon": "streamline-flex:home-2",
                "title": "首页",
                "affixTab": True,
                "order": 0
            }
        })

        # 订单管理菜单 - 只检查主菜单权限
        if 'MENU_ORDER' in permission_codes:
            order_menu = {
                "id": 2,
                "name": "Order",
                "path": "/order",
                "redirect": "/order/list",
                "meta": {
                    "icon": "lucide:shopping-cart",
                    "title": "订单管理",
                    "order": 1
                },
                "children": []
            }

            # 订单列表子菜单 - 只检查子菜单权限
            if 'MENU_ORDERLIST' in permission_codes:
                order_menu["children"].append({
                    "id": 3,
                    "name": "OrderList",
                    "path": "/order/list",
                    "component": "/order/orderlist/index",
                    "meta": {
                        "icon": "lucide:list",
                        "title": "订单列表",
                        "order": 2
                    }
                })

            # 回款管理子菜单 - 只检查子菜单权限
            if 'MENU_PAYMENTRECEIPT' in permission_codes:
                order_menu["children"].append({
                    "id": 3,
                    "name": "PaymentReceipt",
                    "path": "/order/payment",
                    "component": "/order/payment/index",
                    "meta": {
                        "icon": "lucide:file-text",
                        "title": "回款管理",
                        "order": 3
                    }
                })

            # 订单统计子菜单 - 只检查子菜单权限
            if 'MENU_ORDERSTAT' in permission_codes:
                order_menu["children"].append({
                    "id": 4,
                    "name": "OrderStat",
                    "path": "/order/stat",
                    "component": "/order/orderstat/index",
                    "meta": {
                        "icon": "lucide:bar-chart",
                        "title": "订单统计",
                        "order": 4
                    }
                })
            # 订单日历子菜单 - 只检查子菜单权限
            if 'MENU_ORDERCALENDAR' in permission_codes:
                order_menu["children"].append({
                    "id": 5,
                    "name": "OrderCalendar",
                    "path": "/order/calendar",
                    "component": "/order/calendar/index",
                    "meta": {
                        "icon": "lucide:calendar",
                        "title": "订单日历",
                        "order": 5
                    }
                })

            menus.append(order_menu)

        # 财务管理菜单 - 只检查主菜单权限
        if 'MENU_FINANCE' in permission_codes:
            finance_menu = {
                "id": 6,
                "name": "Finance",
                "path": "/finance",
                "redirect": "/finance/record",
                "meta": {
                    "icon": "ph:currency-dollar-light",
                    "title": "财务管理",
                    "order": 1
                },
                "children": []
            }

            # 收支记录子菜单 - 只检查子菜单权限
            if 'MENU_RECORD' in permission_codes:
                finance_menu["children"].append({
                    "id": 7,
                    "name": "Record",
                    "path": "/finance/record",
                    "component": "/finance/record/index",
                    "meta": {
                        "icon": "lucide:file-text",
                        "title": "收支登记",
                        "order": 2
                    }
                })

            # 回款登记子菜单 - 只检查子菜单权限
            if 'MENU_FINANCEPAYMENT' in permission_codes:
                finance_menu["children"].append({
                    "id": 8,
                    "name": "FinancePayment",
                    "path": "/finance/payment",
                    "component": "/finance/payment/index",
                    "meta": {
                        "icon": "lucide:file-text",
                        "title": "回款登记",
                        "order": 3
                    }
                })


            # 报销管理子菜单 - 只检查子菜单权限
            if 'MENU_REIMBURSE' in permission_codes:
                finance_menu["children"].append({
                    "id": 9,
                    "name": "Reimburse",
                    "path": "/finance/reimburse",
                    "component": "/finance/reimburse/index",
                    "meta": {
                        "icon": "tdesign:money",
                        "title": "报销管理",
                        "order": 4
                    }
                })

            # 报表管理子菜单 - 只检查子菜单权限
            if 'MENU_REPORT' in permission_codes:
                finance_menu["children"].append({
                    "id": 10,
                    "name": "Report",
                    "path": "/finance/report",
                    "component": "/finance/report/index",
                    "meta": {
                        "icon": "lucide:chart-line",
                        "title": "报表管理",
                        "order": 5
                    }
                })
            # 收支日历子菜单 - 只检查子菜单权限
            if 'MENU_FINANCECALENDAR' in permission_codes:
                finance_menu["children"].append({
                    "id": 11,
                    "name": "FinanceCalendar",
                    "path": "/finance/calendar",
                    "component": "/finance/calendar/index",
                    "meta": {
                        "icon": "lucide:calendar",
                        "title": "收支日历",
                        "order": 6
                    }
                })

            menus.append(finance_menu)

        # 基础设置菜单 - 只检查主菜单权限
        if 'MENU_SETTING' in permission_codes:
            setting_menu = {
                "id": 12,
                "name": "Setting",
                "path": "/setting",
                "redirect": "/setting/income",
                "meta": {
                    "icon": "lucide:settings",
                    "title": "基础设置",
                    "order": 1
                },
                "children": []
            }

            # 收入设置子菜单 - 只检查子菜单权限
            if 'MENU_INCOME' in permission_codes:
                setting_menu["children"].append({
                    "id": 13,
                    "name": "Income",
                    "path": "/setting/income",
                    "component": "/setting/income/index",
                    "meta": {
                        "icon": "icon-park-outline:income-one",
                        "title": "收入设置",
                        "order": 2
                    }
                })

            # 支出设置子菜单 - 只检查子菜单权限
            if 'MENU_EXPENDITURE' in permission_codes:
                setting_menu["children"].append({
                    "id": 14,
                    "name": "Expenditure",
                    "path": "/setting/expenditure",
                    "component": "/setting/expenditure/index",
                    "meta": {
                        "icon": "icon-park-outline:expenses-one",
                        "title": "支出设置",
                        "order": 3
                    }
                })

            # 线上店铺子菜单 - 只检查子菜单权限
            if 'MENU_STORE_ONLINE' in permission_codes:
                setting_menu["children"].append({
                    "id": 15,
                    "name": "OnlineStore",
                    "path": "/setting/online-store",
                    "component": "/setting/online-store/index",
                    "meta": {
                        "icon": "lucide:link",
                        "title": "线上店铺",
                        "order": 4
                    }
                })

            # 线下店铺子菜单 - 只检查子菜单权限
            if 'MENU_STORE_OFFLINE' in permission_codes:
                setting_menu["children"].append({
                    "id": 16,
                    "name": "OfflineStore",
                    "path": "/setting/offline-store",
                    "component": "/setting/offline-store/index",
                    "meta": {
                        "icon": "lucide:map-pin",
                        "title": "线下店铺",
                        "order": 5
                    }
                })

            # 只有当有子菜单时才添加基础设置菜单
            if setting_menu["children"]:
                menus.append(setting_menu)

        # 系统管理菜单 - 只检查主菜单权限
        if 'MENU_SYSTEM' in permission_codes:
            system_menu = {
                "id": 17,
                "name": "manage",
                "path": "/manage",
                "redirect": "/manage/user",
                "meta": {
                    "icon": "grommet-icons:system",
                    "title": "系统管理",
                    "order": 1
                },
                "children": []
            }

            # 用户管理子菜单 - 只检查子菜单权限
            if 'MENU_USER' in permission_codes:
                system_menu["children"].append({
                    "id": 18,
                    "name": "UserManage",
                    "path": "/manage/user",
                    "component": "/manage/user/index",
                    "meta": {
                        "icon": "mdi:account-multiple",
                        "title": "用户管理",
                        "order": 2
                    }
                })

            # 角色管理子菜单 - 只检查子菜单权限
            if 'MENU_ROLE' in permission_codes:
                system_menu["children"].append({
                    "id": 19,
                    "name": "RoleManage",
                    "path": "/manage/role",
                    "component": "/manage/role/index",
                    "meta": {
                        "icon": "mdi:account-key",
                        "title": "角色管理",
                        "order": 3
                    }
                })

            # 权限管理子菜单 - 只检查子菜单权限
            if 'MENU_PERMISSION_MANAGER' in permission_codes:
                system_menu["children"].append({
                    "id": 120,
                    "name": "PermissionManager",
                    "path": "/manage/rbac",
                    "component": "/manage/rbac/index",
                    "meta": {
                        "icon": "mdi:shield-key",
                        "title": "权限管理",
                        "order": 4
                    }
                })
            # 数据备份子菜单 - 只检查子菜单权限
            if 'MENU_BACKUP' in permission_codes:
                system_menu["children"].append({
                    "id": 21,
                    "name": "Backup",
                    "path": "/manage/backup",
                    "component": "/manage/backup/index",
                    "meta": {
                        "icon": "mdi:database",
                        "title": "数据备份",
                        "order": 5
                    }
                })

            # 只有当有子菜单时才添加系统管理菜单
            if system_menu["children"]:
                menus.append(system_menu)

        return ResponseUtils.success(menus, "获取菜单成功")

    except Exception as e:
        import traceback
        print(f"获取菜单错误: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return ResponseUtils.error(f"获取菜单失败: {str(e)}", 500)


