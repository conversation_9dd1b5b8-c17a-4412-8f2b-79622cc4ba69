import { requestClient } from './request';

/**
 * 权限信息接口
 */
export interface PermissionInfo {
  id: number;
  code: string;
  name: string;
  description?: string;
  type: 'menu' | 'button';
  parent_code?: string;
}

/**
 * 获取所有权限
 * @returns 权限列表
 */
export async function fetchAllPermissions() {
  return requestClient.get('/permission/all');
}

/**
 * 获取用户权限
 * @param userId 用户ID
 * @returns 用户权限列表
 */
export async function fetchUserPermissions(userId: number) {
  return requestClient.get(`/permission/user/${userId}`);
}

/**
 * 获取用户权限码
 * @param userId 用户ID
 * @returns 用户权限码列表
 */
export async function fetchUserPermissionCodes(userId: number) {
  return requestClient.get(`/permission/user/${userId}/codes`);
}

/**
 * 检查权限
 * @param userId 用户ID
 * @param permissionCode 权限码
 * @returns 权限检查结果
 */
export async function checkPermission(userId: number, permissionCode: string) {
  return requestClient.post('/permission/check', {
    user_id: userId,
    permission_code: permissionCode
  });
}
