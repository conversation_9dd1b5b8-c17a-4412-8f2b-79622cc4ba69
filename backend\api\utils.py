#!/usr/bin/env python3
"""API工具函数"""

import psycopg2
import psycopg2.extras
from flask import jsonify
from config.config import Config

def get_db_connection():
    """获取数据库连接"""
    conn = psycopg2.connect(**Config.DATABASE_CONFIG)
    return conn

def format_response(success=True, data=None, message="操作成功", total=None, page=None, pageSize=None, error=None):
    """格式化响应数据"""
    response = {
        "code": 0 if success else 1,
        "success": success,
        "message": message
    }

    if data is not None:
        response["data"] = data

    if total is not None:
        response["total"] = total

    if page is not None:
        response["page"] = page

    if pageSize is not None:
        response["pageSize"] = pageSize

    if error is not None:
        response["error"] = error

    return jsonify(response)

def validate_required_fields(data, required_fields):
    """验证必填字段"""
    if not data:
        return format_response(success=False, message="请求数据不能为空")
    
    missing_fields = []
    for field in required_fields:
        if field not in data or not data[field] or (isinstance(data[field], str) and not data[field].strip()):
            missing_fields.append(field)
    
    if missing_fields:
        return format_response(success=False, message=f"缺少必填字段: {', '.join(missing_fields)}")
    
    return None
