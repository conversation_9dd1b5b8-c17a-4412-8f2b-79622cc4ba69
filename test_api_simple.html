<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>日历API测试</h1>
    <button onclick="testCalendarAPI()">测试日历API</button>
    <button onclick="testStoreAPI()">测试店铺API</button>
    <div id="result"></div>

    <script>
        // 获取当前月份的开始和结束日期
        function getCurrentMonthRange() {
            const now = new Date();
            const start = new Date(now.getFullYear(), now.getMonth(), 1);
            const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            
            return {
                start: start.toISOString().split('T')[0],
                end: end.toISOString().split('T')[0]
            };
        }

        async function testStoreAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>测试店铺API...</p>';
            
            try {
                const response = await fetch('/api/online-stores', {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
                    }
                });
                
                const data = await response.json();
                console.log('店铺API响应:', data);
                
                resultDiv.innerHTML = `
                    <h3>店铺API结果:</h3>
                    <p>状态: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('店铺API错误:', error);
                resultDiv.innerHTML = `<p style="color: red;">店铺API错误: ${error.message}</p>`;
            }
        }

        async function testCalendarAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>测试日历API...</p>';
            
            const { start, end } = getCurrentMonthRange();
            console.log('测试日期范围:', start, '到', end);
            
            try {
                const url = `/api/orders/calendar?start_date=${start}&end_date=${end}`;
                console.log('请求URL:', url);
                
                const response = await fetch(url, {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
                    }
                });
                
                const data = await response.json();
                console.log('日历API响应:', data);
                
                resultDiv.innerHTML = `
                    <h3>日历API结果:</h3>
                    <p>状态: ${response.status}</p>
                    <p>日期范围: ${start} 到 ${end}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('日历API错误:', error);
                resultDiv.innerHTML = `<p style="color: red;">日历API错误: ${error.message}</p>`;
            }
        }

        // 页面加载时显示当前token状态
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('找到token:', token.substring(0, 20) + '...');
            } else {
                console.log('未找到token，请先登录');
                document.getElementById('result').innerHTML = '<p style="color: orange;">未找到token，请先登录系统</p>';
            }
        };
    </script>
</body>
</html>
