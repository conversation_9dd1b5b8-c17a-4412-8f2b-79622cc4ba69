#!/usr/bin/env python3
"""
修复主键序列冲突的脚本

当使用批量导入脚本导入数据后，如果直接插入了ID值，
会导致PostgreSQL的序列计数器没有更新，从而在新增数据时产生主键冲突。
此脚本用于修复这个问题。
"""

import psycopg2
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def fix_sequence_conflicts():
    """修复主键序列冲突问题"""
    try:
        # 连接到PostgreSQL数据库
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()

        print("🔧 开始修复主键序列冲突问题...")
        
        # 需要修复的表和对应的序列名
        tables_to_fix = [
            ('orderlist', 'orderlist_id_seq'),
            ('paymentreceipt', 'paymentreceipt_id_seq'),
            ('finance', 'finance_id_seq'),
            ('financepayment', 'financepayment_id_seq'),
            ('users', 'users_id_seq'),
            ('roles', 'roles_id_seq'),
            ('permissions', 'permissions_id_seq'),
            ('user_roles', 'user_roles_id_seq'),
            ('role_permissions', 'role_permissions_id_seq'),
            ('income_categories', 'income_categories_id_seq'),
            ('expenditure_categories', 'expenditure_categories_id_seq'),
            ('online_stores', 'online_stores_id_seq'),
            ('offline_stores', 'offline_stores_id_seq')
        ]
        
        for table_name, sequence_name in tables_to_fix:
            try:
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    )
                """, (table_name,))
                
                if not cursor.fetchone()[0]:
                    print(f"  ⚠️  表 {table_name} 不存在，跳过")
                    continue
                
                # 检查表中是否有数据
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                
                if row_count == 0:
                    print(f"  ℹ️  表 {table_name} 为空，跳过")
                    continue
                
                # 获取当前最大ID
                cursor.execute(f"SELECT MAX(id) FROM {table_name}")
                max_id = cursor.fetchone()[0]
                
                if max_id is None:
                    print(f"  ℹ️  表 {table_name} 没有ID字段，跳过")
                    continue
                
                # 获取当前序列值
                cursor.execute(f"SELECT last_value FROM {sequence_name}")
                current_seq_value = cursor.fetchone()[0]
                
                print(f"  📊 表 {table_name}:")
                print(f"     - 记录数: {row_count}")
                print(f"     - 最大ID: {max_id}")
                print(f"     - 当前序列值: {current_seq_value}")
                
                # 如果序列值小于最大ID，需要修复
                if current_seq_value < max_id:
                    # 重置序列到正确的值
                    cursor.execute(f"SELECT setval('{sequence_name}', {max_id})")
                    print(f"     ✅ 已将序列 {sequence_name} 重置为 {max_id}")
                else:
                    print(f"     ✅ 序列 {sequence_name} 无需修复")
                    
            except Exception as e:
                print(f"  ❌ 修复表 {table_name} 时出错: {str(e)}")
                continue
        
        conn.commit()
        print("\n✅ 主键序列冲突修复完成！")
        print("现在可以正常使用前端新增功能了。")
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    finally:
        if conn:
            conn.close()
            print("数据库连接已关闭")

def check_sequence_status():
    """检查序列状态"""
    try:
        # 连接到PostgreSQL数据库
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()

        print("📊 检查序列状态...")
        
        # 获取所有序列信息
        cursor.execute("""
            SELECT 
                schemaname,
                sequencename,
                last_value,
                start_value,
                increment_by,
                max_value,
                min_value,
                cache_value,
                is_cycled,
                is_called
            FROM pg_sequences 
            WHERE schemaname = 'public'
            ORDER BY sequencename
        """)
        
        sequences = cursor.fetchall()
        
        for seq in sequences:
            schema, seq_name, last_value, start_value, increment_by, max_value, min_value, cache_value, is_cycled, is_called = seq
            print(f"  📈 {seq_name}:")
            print(f"     - 当前值: {last_value}")
            print(f"     - 起始值: {start_value}")
            print(f"     - 增量: {increment_by}")
            print(f"     - 是否已调用: {is_called}")
            
            # 检查对应的表
            table_name = seq_name.replace('_id_seq', '')
            try:
                cursor.execute(f"SELECT MAX(id) FROM {table_name}")
                max_id = cursor.fetchone()[0]
                if max_id is not None:
                    print(f"     - 表最大ID: {max_id}")
                    if last_value < max_id:
                        print(f"     ⚠️  序列值小于表最大ID，可能存在冲突！")
                    else:
                        print(f"     ✅ 序列值正常")
                else:
                    print(f"     - 表为空")
            except:
                print(f"     - 无对应表或表结构不匹配")
            print()
        
    except Exception as e:
        print(f"❌ 检查序列状态时出错: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修复PostgreSQL主键序列冲突')
    parser.add_argument('--check', action='store_true', help='只检查序列状态，不进行修复')
    parser.add_argument('--fix', action='store_true', help='修复序列冲突')
    
    args = parser.parse_args()
    
    if args.check:
        check_sequence_status()
    elif args.fix:
        fix_sequence_conflicts()
    else:
        print("请指定操作:")
        print("  --check  检查序列状态")
        print("  --fix    修复序列冲突")
        print("\n示例:")
        print("  python fix_sequence_conflicts.py --check")
        print("  python fix_sequence_conflicts.py --fix")
