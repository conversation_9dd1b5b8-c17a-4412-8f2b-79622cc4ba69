<template>
  <ElDialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="创建数据备份"
    width="600px"
    :close-on-click-modal="false"
  >
    <ElForm :model="backupForm" label-width="100px">
      <ElFormItem label="备份名称" required>
        <ElInput v-model="backupForm.name" placeholder="请输入备份名称" />
      </ElFormItem>

      <ElFormItem label="备份类型" required>
        <ElSelect v-model="backupForm.type" style="width: 100%">
          <ElOption label="完整备份" value="full" />
          <ElOption label="部分备份" value="partial" />
        </ElSelect>
      </ElFormItem>



      <ElFormItem v-if="backupForm.type === 'partial'" label="选择表">
        <div class="table-selection">
          <!-- 操作按钮 -->
          <div class="selection-controls">
            <ElButton size="small" @click="selectAllTables">全选</ElButton>
            <ElButton size="small" @click="unselectAllTables">不选</ElButton>
            <ElButton size="small" @click="invertSelection">反向选择</ElButton>
            <span class="selection-info">已选择 {{ selectedTablesCount }} / {{ tables.length }} 个表</span>
          </div>

          <!-- 表格复选框列表 -->
          <ElCheckboxGroup v-model="backupForm.tables" class="table-checkboxes">
            <ElCheckbox
              v-for="table in tables"
              :key="table.name"
              :value="table.name"
              class="table-checkbox"
            >
              <div class="checkbox-content">
                <span class="table-label">{{ table.description }}</span>
                <span class="table-meta">{{ table.records.toLocaleString() }} 条记录 · {{ table.size }}</span>
              </div>
            </ElCheckbox>
          </ElCheckboxGroup>
        </div>
      </ElFormItem>


    </ElForm>

    <!-- 进度显示 -->
    <div v-if="loading" class="progress-container">
      <ElProgress :percentage="backupProgress" :stroke-width="8" />
      <p class="progress-text">正在备份数据，请稍候...</p>
    </div>

    <template #footer>
      <ElSpace>
        <ElButton @click="handleCancel" :disabled="loading">取消</ElButton>
        <ElButton type="primary" @click="handleConfirm" :loading="loading">
          开始备份
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElCheckbox,
  ElCheckboxGroup,
  ElButton,
  ElSpace,
  ElProgress
} from 'element-plus';
import { toBeijingTimeFilename } from '#/utils/common';
import { fetchTableNameMapping, type BackupForm } from '#/api/backup';

interface TableDetail {
  name: string;
  description: string;
  records: number;
  size: string;
  size_bytes: number;
}

interface Props {
  visible: boolean;
  loading?: boolean;
  backupProgress?: number;
  tables: TableDetail[];
  initialForm?: Partial<BackupForm>;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  backupProgress: 0,
  initialForm: () => ({})
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  confirm: [form: BackupForm];
  cancel: [];
}>();

// 动态表名映射
const tableNameMap = ref<Record<string, string>>({});

// 表单数据
const backupForm = ref<BackupForm>({
  name: '',
  type: 'full',
  format: 'custom', // 固定使用.backup格式
  tables: [],
  ...props.initialForm
});

// 生成智能备份名称
const generateBackupName = () => {
  const timestamp = toBeijingTimeFilename(new Date());

  if (backupForm.value.type === 'full') {
    return `完整备份_${timestamp}`;
  } else {
    // 部分备份：获取选中表的友好名称
    const selectedTableNames = backupForm.value.tables
      .map(tableName => tableNameMap.value[tableName] || tableName)
      .join('_');

    return `部分备份_${selectedTableNames}_${timestamp}`;
  }
};

// 已选择的表格数量
const selectedTablesCount = computed(() => {
  return backupForm.value.tables.length;
});

// 全选
const selectAllTables = () => {
  backupForm.value.tables = props.tables.map(table => table.name);
};

// 反选（清空选择）
const unselectAllTables = () => {
  backupForm.value.tables = [];
};

// 反向选择
const invertSelection = () => {
  const allTableNames = props.tables.map(table => table.name);
  const currentSelected = backupForm.value.tables;
  backupForm.value.tables = allTableNames.filter(name => !currentSelected.includes(name));
};

// 处理确认
const handleConfirm = () => {
  emit('confirm', { ...backupForm.value });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};

// 监听备份类型和选择的表，自动更新备份名称
watch([() => backupForm.value.type, () => backupForm.value.tables], () => {
  backupForm.value.name = generateBackupName();
}, { deep: true });

// 加载表名映射
const loadTableNameMapping = async () => {
  try {
    const mapping = await fetchTableNameMapping();
    tableNameMap.value = mapping;
  } catch (error) {
    console.error('加载表名映射失败:', error);
    // 使用空映射，将使用表名本身
    tableNameMap.value = {};
  }
};

// 监听visible变化，重置表单并加载映射
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 先加载表名映射
    await loadTableNameMapping();

    // 然后重置表单
    backupForm.value = {
      name: generateBackupName(),
      type: 'full',
      format: 'custom', // 固定使用.backup格式
      tables: [],
      ...props.initialForm
    };
  }
});
</script>

<style scoped>
.table-selection {
  border: 1px solid #DCDFE6;
  border-radius: 6px;
  padding: 12px;
  background-color: #FAFAFA;
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.selection-info {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
}

.table-checkboxes {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.table-checkbox {
  margin: 0;
  padding: 8px;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  background: white;
  transition: all 0.3s ease;
}

.table-checkbox:hover {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.checkbox-content {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.table-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.table-meta {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.progress-container {
  margin: 20px 0;
  text-align: center;
}

.progress-text {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

/* 滚动条样式 */
.table-checkboxes::-webkit-scrollbar {
  width: 6px;
}

.table-checkboxes::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-checkboxes::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-checkboxes::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
