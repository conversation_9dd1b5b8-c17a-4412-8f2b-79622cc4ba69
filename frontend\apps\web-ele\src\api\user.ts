import { requestClient } from './request';

/**
 * 用户管理API接口
 */

// 用户信息接口类型定义
export interface UserInfo {
  id: number;
  username: string;
  phone: string;
  email?: string;
  role: string;
  status: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt?: string;
}

// 用户列表查询参数
export interface UserListParams {
  page: number;
  pageSize: number;
  username?: string;
  phone?: string;
  status?: string;
}

// 用户列表响应数据
export interface UserListResponse {
  data: UserInfo[];
  total: number;
  page: number;
  page_size: number;
}

// 新增/编辑用户参数
export interface UserOperateParams {
  username: string;
  phone?: string;
  password?: string;
  role?: string;
  status?: number;
}

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表数据
 */
export async function fetchUserList(params: UserListParams) {
  return requestClient.get<UserListResponse>('/user/list', {
    params
  });
}

/**
 * 新增用户
 * @param data 用户数据
 * @returns 操作结果
 */
export async function addUser(data: UserOperateParams) {
  return requestClient.post('/user/create', data);
}

/**
 * 更新用户信息
 * @param id 用户ID
 * @param data 用户数据
 * @returns 操作结果
 */
export async function updateUser(id: number, data: UserOperateParams) {
  return requestClient.put(`/user/update/${id}`, data);
}

/**
 * 删除用户
 * @param id 用户ID
 * @returns 操作结果
 */
export async function deleteUser(id: number) {
  return requestClient.delete(`/user/delete/${id}`);
}

/**
 * 批量删除用户
 * @param ids 用户ID数组
 * @returns 操作结果
 */
export async function batchDeleteUser(ids: number[]) {
  return requestClient.post('/user/batch-delete', { userIds: ids });
}

/**
 * 更新用户状态
 * @param id 用户ID
 * @param status 状态
 * @returns 操作结果
 */
export async function updateUserStatus(id: number, status: boolean) {
  return requestClient.put(`/user/status/${id}`, {
    status: status ? 1 : 0
  });
}
