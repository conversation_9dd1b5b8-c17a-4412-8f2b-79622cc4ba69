#!/usr/bin/env python3
"""订单管理API"""

from flask import Blueprint, request, g
from datetime import datetime
from .auth import token_required, check_permission
from .utils import get_db_connection, format_response, validate_required_fields

order_bp = Blueprint('order', __name__, url_prefix='/api')

@order_bp.route('/orders', methods=['GET'])
@token_required
@check_permission('MENU_ORDERLIST')
def get_orders():
    """获取订单列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        date = request.args.get('date')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')
        order_no = request.args.get('order_no')

        # 构建查询条件
        where_conditions = ['isdelete = 0']
        params = []

        # 处理日期查询 - 支持单个日期或日期范围
        if start_date and end_date:
            # 日期范围查询
            where_conditions.append('date >= %s AND date <= %s')
            params.extend([start_date, end_date])
        elif date:
            # 单个日期查询（兼容原有逻辑）
            where_conditions.append('date LIKE %s')
            params.append(f'%{date}%')

        if shop:
            where_conditions.append('shop LIKE %s')
            params.append(f'%{shop}%')

        if order_no:
            where_conditions.append('order_no LIKE %s')
            params.append(f'%{order_no}%')
        
        where_clause = ' AND '.join(where_conditions)
        
        # 查询总数
        count_sql = f'SELECT COUNT(*) FROM orderlist WHERE {where_clause}'
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]
        
        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT id, date, shop, order_no, sale_price, box_fee, delivery_fee,
                   actual_payment, purchase_price, product_profit, total_profit, screenshot
            FROM orderlist
            WHERE {where_clause}
            ORDER BY id DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        
        orders = []
        for row in cursor.fetchall():
            orders.append({
                'id': row[0],
                'date': row[1],
                'shop': row[2],
                'order_no': row[3],
                'sale_price': row[4],
                'box_fee': row[5],
                'delivery_fee': row[6],
                'actual_payment': row[7],
                'purchase_price': row[8],
                'product_profit': row[9],
                'total_profit': row[10],
                'screenshot': row[11]
            })
        
        return format_response(
            success=True,
            data=orders,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        print(f"获取订单列表错误: {str(e)}")
        return format_response(success=False, message="获取订单列表失败")
    finally:
        conn.close()

@order_bp.route('/orders', methods=['POST'])
@token_required
@check_permission('ORDER_ADD')
def create_order():
    """创建订单"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['date', 'shop', 'order_no', 'sale_price', 'actual_payment']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查订单号是否已存在
        cursor.execute('SELECT COUNT(*) FROM orderlist WHERE order_no = %s AND isdelete = 0', (data['order_no'],))
        if cursor.fetchone()[0] > 0:
            return format_response(success=False, message="订单号已存在")

        # 获取screenshot字段
        screenshot = data.get('screenshot', '')

        # 插入订单
        cursor.execute('''
            INSERT INTO orderlist (date, shop, order_no, sale_price, box_fee, delivery_fee,
                                 actual_payment, purchase_price, product_profit, total_profit, screenshot)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        ''', (
            data['date'],
            data['shop'],
            data['order_no'],
            data.get('sale_price', 0),
            data.get('box_fee', 0),
            data.get('delivery_fee', 0),
            data.get('actual_payment', 0),
            data.get('purchase_price', 0),
            data.get('product_profit', 0),
            data.get('total_profit', 0),
            screenshot
        ))

        order_id = cursor.fetchone()[0]
        conn.commit()

        return format_response(
            success=True,
            data={'id': order_id, **data},
            message="订单创建成功"
        )

    except Exception as e:
        print(f"创建订单错误: {str(e)}")
        return format_response(success=False, message="创建订单失败")
    finally:
        conn.close()

@order_bp.route('/orders/<int:order_id>', methods=['PUT'])
@token_required
@check_permission('ORDER_EDIT')
def update_order(order_id):
    """更新订单"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['date', 'shop', 'order_no', 'sale_price', 'actual_payment']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查订单是否存在
        cursor.execute('SELECT COUNT(*) FROM orderlist WHERE id = %s AND isdelete = 0', (order_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="订单不存在")

        # 检查订单号是否被其他订单使用
        cursor.execute('SELECT COUNT(*) FROM orderlist WHERE order_no = %s AND id != %s AND isdelete = 0',
                      (data['order_no'], order_id))
        if cursor.fetchone()[0] > 0:
            return format_response(success=False, message="订单号已被其他订单使用")

        # 获取screenshot字段
        screenshot = data.get('screenshot', '')

        # 更新订单
        cursor.execute('''
            UPDATE orderlist
            SET date = %s, shop = %s, order_no = %s, sale_price = %s, box_fee = %s, delivery_fee = %s,
                actual_payment = %s, purchase_price = %s, product_profit = %s, total_profit = %s, screenshot = %s
            WHERE id = %s
        ''', (
            data['date'],
            data['shop'],
            data['order_no'],
            data.get('sale_price', 0),
            data.get('box_fee', 0),
            data.get('delivery_fee', 0),
            data.get('actual_payment', 0),
            data.get('purchase_price', 0),
            data.get('product_profit', 0),
            data.get('total_profit', 0),
            screenshot,
            order_id
        ))

        conn.commit()

        return format_response(
            success=True,
            data={'id': order_id, **data},
            message="订单更新成功"
        )

    except Exception as e:
        print(f"更新订单错误: {str(e)}")
        return format_response(success=False, message="更新订单失败")
    finally:
        conn.close()

@order_bp.route('/orders/<int:order_id>', methods=['DELETE'])
@token_required
@check_permission('ORDER_DELETE')
def delete_order(order_id):
    """删除订单"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查订单是否存在
        cursor.execute('SELECT COUNT(*) FROM orderlist WHERE id = %s AND isdelete = 0', (order_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="订单不存在")

        # 软删除订单
        cursor.execute('UPDATE orderlist SET isdelete = 1 WHERE id = %s', (order_id,))
        conn.commit()
        
        return format_response(success=True, message="订单删除成功")
        
    except Exception as e:
        print(f"删除订单错误: {str(e)}")
        return format_response(success=False, message="删除订单失败")
    finally:
        conn.close()

@order_bp.route('/orders/batch-delete', methods=['POST'])
@token_required
@check_permission('ORDER_BATCH_DELETE')
def batch_delete_orders():
    """批量删除订单"""
    try:
        data = request.get_json()
        order_ids = data.get('ids', [])
        
        if not order_ids:
            return format_response(success=False, message="请选择要删除的订单")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 批量软删除
        placeholders = ','.join(['%s' for _ in order_ids])
        cursor.execute(f'UPDATE orderlist SET isdelete = 1 WHERE id IN ({placeholders})', order_ids)
        conn.commit()
        
        return format_response(success=True, message="批量删除成功")
        
    except Exception as e:
        print(f"批量删除订单错误: {str(e)}")
        return format_response(success=False, message="批量删除失败")
    finally:
        conn.close()

@order_bp.route('/orders/calendar', methods=['GET'])
@token_required
@check_permission('MENU_ORDERLIST')
def get_calendar_data():
    """获取日历数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shops = request.args.getlist('shops')  # 获取多个店铺参数

        print(f"接收到的参数: start_date={start_date}, end_date={end_date}, shops={shops}")

        if not start_date or not end_date:
            return format_response(success=False, message="请提供开始和结束日期")

        # 构建查询条件
        where_conditions = ['isdelete = 0']
        params = []

        # 日期范围条件
        where_conditions.append('date >= %s AND date <= %s')
        params.extend([start_date, end_date])

        # 店铺条件
        if shops and len(shops) > 0:
            shop_placeholders = ','.join(['%s' for _ in shops])
            where_conditions.append(f'shop IN ({shop_placeholders})')
            params.extend(shops)
            print(f"添加店铺筛选条件: {shops}")

        where_clause = ' AND '.join(where_conditions)

        # 查询订单数据，按店铺和日期分组
        # 使用actual_payment作为实际销售额，同时获取买入价用于计算净利润
        order_sql = f'''
            SELECT
                shop,
                date,
                COUNT(*) as order_count,
                COALESCE(SUM(actual_payment), 0) as total_sales,
                COALESCE(SUM(purchase_price), 0) as total_purchase_price
            FROM orderlist
            WHERE {where_clause}
            GROUP BY shop, date
            ORDER BY shop, date
        '''

        print(f"执行订单SQL: {order_sql}")
        print(f"SQL参数: {params}")
        cursor.execute(order_sql, params)
        order_results = cursor.fetchall()
        print(f"订单查询结果: {len(order_results)} 条记录")

        # 查询回款数据，需要处理日期偏移
        # 回款日期的前一天对应订单日期，所以我们需要查询回款日期范围为 start_date+1 到 end_date+1
        from datetime import datetime, timedelta

        # 计算回款查询的日期范围（比订单日期范围晚一天）
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        payment_start_date = (start_date_obj + timedelta(days=1)).strftime('%Y-%m-%d')
        payment_end_date = (end_date_obj + timedelta(days=1)).strftime('%Y-%m-%d')

        payment_sql = f'''
            SELECT
                shop,
                date as payment_date,
                COALESCE(SUM(amount), 0) as payment_amount
            FROM paymentreceipt
            WHERE is_deleted = 0
            AND date >= %s
            AND date <= %s
        '''

        payment_params = [payment_start_date, payment_end_date]

        # 如果指定了店铺，也要过滤回款数据
        if shops and len(shops) > 0:
            payment_sql += f' AND shop IN ({shop_placeholders})'
            payment_params.extend(shops)

        payment_sql += ' GROUP BY shop, date'

        # 执行回款查询
        print(f"执行回款SQL: {payment_sql}")
        print(f"回款SQL参数: {payment_params}")
        cursor.execute(payment_sql, payment_params)
        payment_results = cursor.fetchall()
        print(f"回款查询结果: {len(payment_results)} 条记录")

        # 组织数据结构
        calendar_data = {}

        # 处理订单数据
        for row in order_results:
            shop = row[0]
            date = str(row[1])
            order_count = int(row[2])
            total_sales = float(row[3])
            total_purchase_price = float(row[4])

            if shop not in calendar_data:
                calendar_data[shop] = {}

            calendar_data[shop][date] = {
                'date': date,
                'orderCount': order_count,
                'totalSales': total_sales,
                'purchasePrice': total_purchase_price,  # 保存买入价，用于计算净利润
                'netProfit': 0  # 默认为0，后面会更新
            }

        # 处理回款数据（净利润）
        # 回款日期对应前一天的订单，所以需要计算日期偏移
        for row in payment_results:
            shop = row[0]
            payment_date_str = str(row[1])
            payment_amount = float(row[2])

            # 计算对应的订单日期（回款日期减1天）
            payment_date_obj = datetime.strptime(payment_date_str, '%Y-%m-%d')
            order_date_obj = payment_date_obj - timedelta(days=1)
            order_date_str = order_date_obj.strftime('%Y-%m-%d')

            # 如果对应的订单日期存在数据，计算净利润
            if shop in calendar_data and order_date_str in calendar_data[shop]:
                purchase_price = calendar_data[shop][order_date_str]['purchasePrice']
                net_profit = payment_amount - purchase_price
                calendar_data[shop][order_date_str]['netProfit'] = net_profit
                print(f"计算净利润: {shop} {order_date_str} = {payment_amount} - {purchase_price} = {net_profit}")
            else:
                print(f"未找到对应订单数据: {shop} {order_date_str} (回款日期: {payment_date_str})")

        # 转换为数组格式
        result_data = {}
        for shop, dates_data in calendar_data.items():
            result_data[shop] = list(dates_data.values())

        print(f"最终返回数据: {len(result_data)} 个店铺")
        for shop, data in result_data.items():
            print(f"店铺 {shop}: {len(data)} 天数据")

        return format_response(
            success=True,
            data=result_data,
            message="获取日历数据成功"
        )

    except Exception as e:
        print(f"获取日历数据错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return format_response(success=False, message=f"获取日历数据失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

@order_bp.route('/online-stores', methods=['GET'])
@token_required
@check_permission('MENU_ORDERLIST')
def get_online_stores_for_orders():
    """获取线上店铺列表（用于订单相关功能）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有启用的店铺
        cursor.execute('''
            SELECT id, name, status, created_at, updated_at
            FROM online_stores
            WHERE isdelete = 0 AND status = 1
            ORDER BY created_at DESC
        ''')

        stores = []
        for row in cursor.fetchall():
            stores.append({
                'id': row[0],
                'name': row[1],
                'status': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        return format_response(
            success=True,
            data=stores,
            message="获取店铺列表成功"
        )

    except Exception as e:
        print(f"获取店铺列表错误: {str(e)}")
        return format_response(success=False, message="获取店铺列表失败")
    finally:
        conn.close()
