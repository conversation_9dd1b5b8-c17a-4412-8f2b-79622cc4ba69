import os
from flask import Flask, jsonify, send_from_directory
from flask_cors import CORS
from config.config import Config
from api.auth import auth_bp
from api.user import user_bp
from api.menu import menu_bp
from api.role import role_bp
from api.permission import permission_bp
from api.permission_manager import permission_manager_bp
from api.income import income_bp
from api.expenditure import expenditure_bp
from api.online_store import online_store_bp
from api.offline_store import offline_store_bp
from api.order import order_bp
from api.payment import payment_bp
from api.upload import upload_bp
from api.finance import finance_bp
from api.financepayment import financepayment_bp
from api.categories import categories_bp
from api.report import report_bp
from api.order_report import order_report_bp
from api.backup_stats import backup_stats_bp
from api.backup import backup_bp

def create_app():
    """应用工厂函数"""
    app = Flask(__name__, static_folder='static', static_url_path='/static')

    # 加载配置
    app.config.from_object(Config)

    # 配置CORS
    CORS(app, origins=Config.CORS_ORIGINS, supports_credentials=True)

    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(menu_bp)
    app.register_blueprint(role_bp)
    app.register_blueprint(permission_bp)
    app.register_blueprint(permission_manager_bp)
    app.register_blueprint(income_bp)
    app.register_blueprint(expenditure_bp)
    app.register_blueprint(online_store_bp)
    app.register_blueprint(offline_store_bp)
    app.register_blueprint(order_bp)
    app.register_blueprint(payment_bp)
    app.register_blueprint(upload_bp)
    app.register_blueprint(finance_bp)
    app.register_blueprint(financepayment_bp)
    app.register_blueprint(categories_bp)
    app.register_blueprint(report_bp)
    app.register_blueprint(order_report_bp)
    app.register_blueprint(backup_stats_bp)
    app.register_blueprint(backup_bp)

    # 静态文件服务
    @app.route('/static/uploads/<filename>')
    def uploaded_file(filename):
        upload_dir = os.path.join(os.getcwd(), 'static', 'uploads')
        return send_from_directory(upload_dir, filename)

    # 健康检查接口
    @app.route('/')
    def hello():
        return jsonify({
            'message': 'Vue Vben Admin Backend API',
            'status': 'success',
            'version': '1.0.0'
        })

    @app.route('/api/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'service': 'Vue Vben Admin Backend'
        })

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
