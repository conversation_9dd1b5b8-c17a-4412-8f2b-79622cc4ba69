import psycopg2
import psycopg2.extras
import bcrypt
from datetime import datetime
from config.config import Config

class User:
    """用户模型类"""
    
    def __init__(self):
        self.db_config = Config.DATABASE_CONFIG
    
    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(**self.db_config)
        return conn
    
    def find_by_username_or_phone(self, identifier):
        """根据用户名或手机号查找用户"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT * FROM users 
            WHERE (username = %s OR phone = %s) 
            AND is_deleted = 0 
            AND status = 1
        ''', (identifier, identifier))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return dict(user)
        return None
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    
    def update_last_login(self, user_id):
        """更新最后登录时间"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users
            SET last_login = %s, updated_at = %s
            WHERE id = %s
        ''', (datetime.now(), datetime.now(), user_id))
        
        conn.commit()
        conn.close()
    
    def get_user_by_id(self, user_id):
        """根据ID获取用户信息"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT id, username, phone, role, status, last_login, created_at
            FROM users
            WHERE id = %s AND is_deleted = 0
        ''', (user_id,))

        user = cursor.fetchone()

        if user:
            user_dict = dict(user)
            # 获取用户的角色信息
            user_dict['roles'] = self.get_user_roles(user_id)
            conn.close()
            return user_dict

        conn.close()
        return None

    def get_user_roles(self, user_id):
        """获取用户的角色列表"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT r.id, r.name, r.description
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = %s AND r.status = 1 AND r.is_deleted = 0
        ''', (user_id,))
        
        roles = cursor.fetchall()
        conn.close()
        
        return [dict(role) for role in roles]

    def assign_roles_to_user(self, user_id, role_ids):
        """为用户分配角色"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查用户是否存在
            cursor.execute('SELECT id FROM users WHERE id = %s AND is_deleted = 0', (user_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '用户不存在'}

            # 删除用户现有角色
            cursor.execute('DELETE FROM user_roles WHERE user_id = %s', (user_id,))

            # 分配新角色
            for role_id in role_ids:
                # 检查角色是否存在
                cursor.execute('SELECT id FROM roles WHERE id = %s AND is_deleted = 0', (role_id,))
                if cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO user_roles (user_id, role_id, created_at)
                        VALUES (%s, %s, %s)
                    ''', (user_id, role_id, datetime.now()))

            conn.commit()
            conn.close()
            return {'success': True, 'message': '角色分配成功'}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'角色分配失败: {str(e)}'}

    def get_user_role_names(self, user_id):
        """获取用户的角色名称列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT r.name
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = %s AND r.status = 1 AND r.is_deleted = 0
        ''', (user_id,))
        
        roles = cursor.fetchall()
        conn.close()
        
        return [role[0] for role in roles]

    def get_user_role_ids(self, user_id):
        """获取用户的角色ID列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT r.id
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = %s AND r.status = 1 AND r.is_deleted = 0
            ORDER BY r.id
        ''', (user_id,))
        
        roles = cursor.fetchall()
        conn.close()
        
        return [role[0] for role in roles]

    def update_user_role_field(self, user_id, role_field_value):
        """更新用户的role字段（兼容性）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users
            SET role = %s, updated_at = %s
            WHERE id = %s
        ''', (role_field_value, datetime.now(), user_id))
        
        conn.commit()
        conn.close()

    def get_users_list(self, page=1, page_size=10, username=None, phone=None, status=None):
        """获取用户列表（分页）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # 构建查询条件
        where_conditions = ['is_deleted = 0']
        params = []

        if username:
            where_conditions.append('username LIKE %s')
            params.append(f'%{username}%')

        if phone:
            where_conditions.append('phone LIKE %s')
            params.append(f'%{phone}%')

        if status is not None:
            where_conditions.append('status = %s')
            params.append(status)

        where_clause = ' AND '.join(where_conditions)

        # 获取总数
        count_sql = f'SELECT COUNT(*) FROM users WHERE {where_clause}'
        cursor.execute(count_sql, params)
        total = cursor.fetchone()['count']

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT id, username, phone, role, status, last_login, created_at, updated_at
            FROM users
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        
        users = cursor.fetchall()
        
        # 为每个用户添加角色信息
        for user in users:
            user_id = user['id']
            cursor.execute('''
                SELECT r.name
                FROM user_roles ur
                JOIN roles r ON ur.role_id = r.id
                WHERE ur.user_id = %s AND r.status = 1 AND r.is_deleted = 0
            ''', (user_id,))
            role_names = [row['name'] for row in cursor.fetchall()]
            user['role_names'] = role_names
        
        conn.close()
        
        return {
            'users': [dict(user) for user in users],
            'total': total,
            'page': page,
            'page_size': page_size
        }

    def create_user(self, username, password, phone=None, role='3', status=1):
        """创建用户"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查用户名是否已存在
            cursor.execute('SELECT id FROM users WHERE username = %s AND is_deleted = 0', (username,))
            if cursor.fetchone():
                return {'success': False, 'message': '用户名已存在'}

            # 检查手机号是否已存在
            if phone:
                cursor.execute('SELECT id FROM users WHERE phone = %s AND is_deleted = 0', (phone,))
                if cursor.fetchone():
                    return {'success': False, 'message': '手机号已存在'}

            # 加密密码
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # 插入用户
            cursor.execute('''
                INSERT INTO users (username, password, phone, role, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (username, hashed_password, phone, role, status, datetime.now(), datetime.now()))

            user_id = cursor.fetchone()[0]
            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户创建成功', 'user_id': user_id}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'创建用户失败: {str(e)}'}

    def update_user(self, user_id, username=None, phone=None, role=None, status=None):
        """更新用户信息"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查用户是否存在
            cursor.execute('SELECT id FROM users WHERE id = %s AND is_deleted = 0', (user_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '用户不存在'}

            update_fields = []
            params = []

            if username is not None:
                # 检查用户名是否被其他用户使用
                cursor.execute('SELECT id FROM users WHERE username = %s AND id != %s AND is_deleted = 0', (username, user_id))
                if cursor.fetchone():
                    return {'success': False, 'message': '用户名已存在'}
                update_fields.append('username = %s')
                params.append(username)

            if phone is not None:
                # 检查手机号是否被其他用户使用
                cursor.execute('SELECT id FROM users WHERE phone = %s AND id != %s AND is_deleted = 0', (phone, user_id))
                if cursor.fetchone():
                    return {'success': False, 'message': '手机号已存在'}
                update_fields.append('phone = %s')
                params.append(phone)

            if role is not None:
                update_fields.append('role = %s')
                params.append(role)

            if status is not None:
                update_fields.append('status = %s')
                params.append(status)

            if not update_fields:
                return {'success': False, 'message': '没有需要更新的字段'}

            # 添加更新时间
            update_fields.append('updated_at = %s')
            params.append(datetime.now())
            params.append(user_id)

            # 执行更新
            update_sql = f'UPDATE users SET {", ".join(update_fields)} WHERE id = %s'
            cursor.execute(update_sql, params)

            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户更新成功'}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'更新用户失败: {str(e)}'}

    def delete_user(self, user_id, current_user_id=None):
        """删除用户（软删除）"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查是否尝试删除自己
            if current_user_id and user_id == current_user_id:
                return {'success': False, 'message': '不能删除自己'}

            # 检查用户是否存在
            cursor.execute('SELECT id FROM users WHERE id = %s AND is_deleted = 0', (user_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '用户不存在'}

            # 软删除用户
            cursor.execute('''
                UPDATE users
                SET is_deleted = 1, updated_at = %s
                WHERE id = %s
            ''', (datetime.now(), user_id))

            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户删除成功'}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'删除用户失败: {str(e)}'}

    def batch_delete_users(self, user_ids, current_user_id=None):
        """批量删除用户（软删除）"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if not user_ids:
                return {'success': False, 'message': '用户ID列表不能为空'}

            # 过滤掉当前用户ID
            if current_user_id:
                user_ids = [uid for uid in user_ids if uid != current_user_id]

            if not user_ids:
                return {'success': False, 'message': '没有可删除的用户'}

            # 构建占位符
            placeholders = ','.join(['%s' for _ in user_ids])

            # 批量软删除
            cursor.execute(f'''
                UPDATE users
                SET is_deleted = 1, updated_at = %s
                WHERE id IN ({placeholders}) AND is_deleted = 0
            ''', [datetime.now()] + user_ids)

            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            return {'success': True, 'message': f'成功删除 {affected_rows} 个用户'}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'批量删除用户失败: {str(e)}'}

    def update_user_status(self, user_id, status):
        """更新用户状态"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查用户是否存在
            cursor.execute('SELECT id FROM users WHERE id = %s AND is_deleted = 0', (user_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '用户不存在'}

            # 更新状态
            cursor.execute('''
                UPDATE users
                SET status = %s, updated_at = %s
                WHERE id = %s
            ''', (status, datetime.now(), user_id))

            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户状态更新成功'}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'更新用户状态失败: {str(e)}'}

    def create_user_with_roles(self, username, password, phone=None, status=1, role_ids=None):
        """创建用户并分配角色"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查用户名是否已存在
            cursor.execute('SELECT id FROM users WHERE username = %s AND is_deleted = 0', (username,))
            if cursor.fetchone():
                return {'success': False, 'message': '用户名已存在'}

            # 检查手机号是否已存在
            if phone:
                cursor.execute('SELECT id FROM users WHERE phone = %s AND is_deleted = 0', (phone,))
                if cursor.fetchone():
                    return {'success': False, 'message': '手机号已存在'}

            # 加密密码
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # 插入用户
            cursor.execute('''
                INSERT INTO users (username, password, phone, role, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (username, hashed_password, phone, '', status, datetime.now(), datetime.now()))

            user_id = cursor.fetchone()[0]

            # 分配角色
            if role_ids:
                for role_id in role_ids:
                    cursor.execute('''
                        INSERT INTO user_roles (user_id, role_id, created_at)
                        VALUES (%s, %s, %s)
                    ''', (user_id, int(role_id), datetime.now()))

            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户创建成功', 'user_id': user_id}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'创建用户失败: {str(e)}'}

    def update_user_with_roles(self, user_id, username=None, phone=None, status=None, role_ids=None):
        """更新用户信息和角色"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查用户是否存在
            cursor.execute('SELECT id FROM users WHERE id = %s AND is_deleted = 0', (user_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '用户不存在'}

            # 检查用户名是否已被其他用户使用
            if username:
                cursor.execute('SELECT id FROM users WHERE username = %s AND id != %s AND is_deleted = 0', (username, user_id))
                if cursor.fetchone():
                    return {'success': False, 'message': '用户名已存在'}

            # 检查手机号是否已被其他用户使用
            if phone:
                cursor.execute('SELECT id FROM users WHERE phone = %s AND id != %s AND is_deleted = 0', (phone, user_id))
                if cursor.fetchone():
                    return {'success': False, 'message': '手机号已存在'}

            # 更新用户基本信息
            update_fields = []
            params = []

            if username is not None:
                update_fields.append('username = %s')
                params.append(username)

            if phone is not None:
                update_fields.append('phone = %s')
                params.append(phone)

            if status is not None:
                update_fields.append('status = %s')
                params.append(status)

            if update_fields:
                update_fields.append('updated_at = %s')
                params.append(datetime.now())
                params.append(user_id)

                update_sql = f'UPDATE users SET {", ".join(update_fields)} WHERE id = %s'
                cursor.execute(update_sql, params)

            # 更新角色分配
            if role_ids is not None:
                # 删除现有角色分配
                cursor.execute('DELETE FROM user_roles WHERE user_id = %s', (user_id,))

                # 添加新的角色分配
                for role_id in role_ids:
                    if role_id:  # 确保role_id不为空
                        cursor.execute('''
                            INSERT INTO user_roles (user_id, role_id, created_at)
                            VALUES (%s, %s, %s)
                        ''', (user_id, int(role_id), datetime.now()))

            conn.commit()
            conn.close()

            return {'success': True, 'message': '用户更新成功'}

        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'更新用户失败: {str(e)}'}

    def get_user_permissions(self, user_id):
        """获取用户的所有权限"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute('''
            SELECT DISTINCT p.id, p.code, p.name, p.type, p.description
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s
            ORDER BY p.id
        ''', (user_id,))

        permissions = cursor.fetchall()
        conn.close()

        return [dict(perm) for perm in permissions]

    def has_permission(self, user_id, permission_code):
        """检查用户是否有指定权限"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 查询用户是否有指定权限
            cursor.execute('''
                SELECT COUNT(*) FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = %s AND p.code = %s
            ''', (user_id, permission_code))

            count = cursor.fetchone()[0]
            return count > 0

        except Exception as e:
            print(f"检查用户权限错误: {str(e)}")
            return False
        finally:
            conn.close()
