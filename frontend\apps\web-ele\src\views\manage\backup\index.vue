<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { toBeijingTimeFilename } from '#/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';

// 导入模块组件
import BackupHeader from './modules/backup-header.vue';
import StatsCards from './modules/stats-cards.vue';
import BackupDialog from './modules/backup-dialog.vue';
import RestoreDialog from './modules/restore-dialog.vue';
import BackupList from './modules/backup-list.vue';

// 导入API
import {
  fetchBackupStats,
  createBackup,
  fetchBackupList,
  restoreBackup,
  type BackupStats,
  type BackupForm,
  type BackupRecord,
  type RestoreForm
} from '../../../api/backup';

defineOptions({ name: 'DataBackup' });

// 备份统计数据
const backupStats = ref<BackupStats>({
  table_count: 0,
  total_records: 0,
  total_size: '0 KB',
  table_details: []
});

// 状态管理
const loading = ref(false);
const backupProgress = ref(0);
const restoreProgress = ref(0);

// 组件引用
const backupListRef = ref();

// 备份记录
const backupRecords = ref<BackupRecord[]>([]);

// 对话框状态
const backupDialogVisible = ref(false);
const restoreDialogVisible = ref(false);

// 表单变量已移动到对话框组件中

// 计算属性
// 这些computed函数已移动到各自的模块组件中

const totalRecords = computed(() => {
  return backupStats.value.total_records;
});

const totalSize = computed(() => {
  return backupStats.value.total_size;
});

const tableCount = computed(() => {
  return backupStats.value.table_count;
});

// 方法
const loadTableStats = async () => {
  loading.value = true;
  try {
    const stats = await fetchBackupStats();


    if (stats && typeof stats === 'object') {
      backupStats.value = {
        table_count: stats.table_count || 0,
        total_records: stats.total_records || 0,
        total_size: stats.total_size || '0 KB',
        table_details: stats.table_details || []
      };



      ElMessage.success('表统计信息加载完成');
    } else {
      throw new Error('API返回数据格式错误');
    }
  } catch (error) {
    console.error('加载表统计信息失败:', error);
    ElMessage.error('加载表统计信息失败');

    // 设置默认值以防止组件报错
    backupStats.value = {
      table_count: 0,
      total_records: 0,
      total_size: '0 KB',
      table_details: []
    };
  } finally {
    loading.value = false;
  }
};

// 加载备份记录
const loadBackupRecords = async () => {
  try {
    const records = await fetchBackupList();
    if (Array.isArray(records)) {
      backupRecords.value = records;
    }
  } catch (error) {
    console.error('加载备份记录失败:', error);
    // 不显示错误消息，因为这不是关键功能
  }
};

const openBackupDialog = () => {
  backupDialogVisible.value = true;
};

const openRestoreDialog = async () => {
  // 加载备份记录
  await loadBackupRecords();
  restoreDialogVisible.value = true;
};

// 对话框处理函数
const getInitialBackupForm = () => {
  const timeStr = toBeijingTimeFilename(new Date());
  return {
    name: `完整备份_${timeStr}`,
    type: 'full' as const,
    tables: []
  };
};

const getInitialRestoreForm = () => {
  return {
    filename: ''
  };
};

const handleBackupConfirm = async (form: BackupForm) => {
  loading.value = true;
  backupProgress.value = 0;

  try {
    // 模拟备份进度
    const interval = setInterval(() => {
      backupProgress.value += Math.random() * 15 + 5; // 随机增长5-20%
      if (backupProgress.value >= 95) {
        backupProgress.value = 95; // 保持在95%，等待实际完成
        clearInterval(interval);
      }
    }, 500);

    // 调用真实的备份API
    const result = await createBackup(form);

    clearInterval(interval);
    backupProgress.value = 100;

    // 由于 requestClient 的 responseReturn: 'data' 配置，
    // 成功的响应会直接返回 data 字段的内容
    if (result && result.file_size) {
      ElMessage.success(`备份创建成功！文件大小: ${result.file_size}`);
      backupDialogVisible.value = false;

      // 刷新统计信息和备份列表
      await loadTableStats();
      if (backupListRef.value) {
        backupListRef.value.refreshList();
      }
    } else {
      throw new Error('备份创建失败');
    }
  } catch (error: any) {
    console.error('备份创建失败:', error);
    ElMessage.error(error.message || '备份创建失败');
  } finally {
    loading.value = false;
    backupProgress.value = 0;
  }
};

const handleBackupCancel = () => {
  backupDialogVisible.value = false;
};

const handleRestoreConfirm = async (form: RestoreForm) => {
  try {
    // 简化确认消息 - 固定使用覆盖模式
    const confirmMessage = '确认要恢复数据吗？此操作将覆盖现有数据！';

    await ElMessageBox.confirm(
      confirmMessage,
      '确认恢复',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    loading.value = true;
    restoreProgress.value = 0;

    // 模拟恢复进度
    const interval = setInterval(() => {
      restoreProgress.value += Math.random() * 10 + 5; // 随机增长5-15%
      if (restoreProgress.value >= 95) {
        restoreProgress.value = 95; // 保持在95%，等待实际完成
        clearInterval(interval);
      }
    }, 800);

    // 调用真实的恢复API
    const result = await restoreBackup(form);

    clearInterval(interval);
    restoreProgress.value = 100;

    if (result && result.filename) {
      ElMessage.success('数据恢复成功！');
      restoreDialogVisible.value = false;

      // 刷新统计信息
      await loadTableStats();
    } else {
      throw new Error('恢复失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('数据恢复失败:', error);
      ElMessage.error(error.message || '数据恢复失败');
    }
  } finally {
    loading.value = false;
    restoreProgress.value = 0;
  }
};

const handleRestoreCancel = () => {
  restoreDialogVisible.value = false;
};

// 旧的函数已移动到对话框组件中

// 旧的恢复函数已移动到对话框组件中

// 这些函数已移动到各自的模块组件中

// 生命周期
onMounted(() => {
  loadTableStats();
});
</script>

<template>
  <div class="backup-container">
    <!-- 页面头部 -->
    <BackupHeader
      :loading="loading"
      @open-backup="openBackupDialog"
      @open-restore="openRestoreDialog"
      @refresh="loadTableStats"
    />

    <!-- 数据统计概览 -->
    <StatsCards
      :table-count="tableCount"
      :total-records="totalRecords"
      :total-size="totalSize"
      :backup-count="0"
    />

    <!-- 备份记录列表 -->
    <BackupList ref="backupListRef" @refresh="loadTableStats" />

    <!-- 备份对话框 -->
    <BackupDialog
      v-model:visible="backupDialogVisible"
      :loading="loading"
      :backup-progress="backupProgress"
      :tables="backupStats.table_details"
      :initial-form="getInitialBackupForm()"
      @confirm="handleBackupConfirm"
      @cancel="handleBackupCancel"
    />

    <!-- 恢复对话框 -->
    <RestoreDialog
      v-model:visible="restoreDialogVisible"
      :loading="loading"
      :restore-progress="restoreProgress"
      :backup-records="backupRecords"
      :initial-form="getInitialRestoreForm()"
      @confirm="handleRestoreConfirm"
      @cancel="handleRestoreCancel"
    />
  </div>
</template>

<style scoped>
.backup-container {
  padding: 10px;
  min-height: calc(100vh - 90px);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 样式已移动到各个模块组件中 */
</style>