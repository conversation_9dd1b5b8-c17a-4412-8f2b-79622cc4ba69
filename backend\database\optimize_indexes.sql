-- 财务报表性能优化索引脚本
-- 执行此脚本来添加必要的数据库索引以提升报表查询性能

-- 连接到数据库
\c postgresql_vue_db;

-- 开始事务
BEGIN;

-- 1. 财务表 (finance) 的索引优化
-- 这些索引主要用于报表聚合查询

-- 复合索引：日期 + 店铺 + 类型 (最常用的查询组合)
CREATE INDEX IF NOT EXISTS idx_finance_date_shop_type 
ON finance (record_date, shop, type) 
WHERE is_deleted = 0;

-- 复合索引：日期 + 类型 (用于按类型统计)
CREATE INDEX IF NOT EXISTS idx_finance_date_type 
ON finance (record_date, type) 
WHERE is_deleted = 0;

-- 复合索引：日期 + 店铺 (用于按店铺统计)
CREATE INDEX IF NOT EXISTS idx_finance_date_shop 
ON finance (record_date, shop) 
WHERE is_deleted = 0;

-- 复合索引：类型 + 分类 (用于分类统计)
CREATE INDEX IF NOT EXISTS idx_finance_type_category 
ON finance (type, category) 
WHERE is_deleted = 0;

-- 复合索引：报销相关查询
CREATE INDEX IF NOT EXISTS idx_finance_reimbursed 
ON finance (type, is_reimbursed, reimbursed_status) 
WHERE is_deleted = 0;

-- 单列索引：日期范围查询
CREATE INDEX IF NOT EXISTS idx_finance_record_date 
ON finance (record_date) 
WHERE is_deleted = 0;

-- 单列索引：店铺查询
CREATE INDEX IF NOT EXISTS idx_finance_shop 
ON finance (shop) 
WHERE is_deleted = 0;

-- 单列索引：类型查询
CREATE INDEX IF NOT EXISTS idx_finance_type 
ON finance (type) 
WHERE is_deleted = 0;

-- 单列索引：分类查询
CREATE INDEX IF NOT EXISTS idx_finance_category 
ON finance (category) 
WHERE is_deleted = 0;

-- 2. 订单表 (orderlist) 的索引优化
-- 这些索引用于订单统计报表查询，针对订单统计页面优化

-- 复合索引：日期 + 店铺 (最重要，用于订单统计主查询)
CREATE INDEX IF NOT EXISTS idx_orderlist_date_shop
ON orderlist (date, shop)
WHERE isdelete = 0;

-- 复合索引：店铺 + 日期 (备选查询路径)
CREATE INDEX IF NOT EXISTS idx_orderlist_shop_date
ON orderlist (shop, date)
WHERE isdelete = 0;

-- 复合索引：日期 + 订单号 (用于订单号搜索)
CREATE INDEX IF NOT EXISTS idx_orderlist_date_orderno
ON orderlist (date, order_no)
WHERE isdelete = 0;

-- 单列索引：日期范围查询
CREATE INDEX IF NOT EXISTS idx_orderlist_date
ON orderlist (date)
WHERE isdelete = 0;

-- 单列索引：店铺查询
CREATE INDEX IF NOT EXISTS idx_orderlist_shop
ON orderlist (shop)
WHERE isdelete = 0;

-- 单列索引：订单号查询
CREATE INDEX IF NOT EXISTS idx_orderlist_order_no
ON orderlist (order_no)
WHERE isdelete = 0;

-- 2.5. 回款表 (paymentreceipt) 的索引优化
-- 这些索引用于订单统计中的回款关联查询，与订单表配合使用

-- 复合索引：日期 + 店铺 (最重要，用于订单统计关联查询)
CREATE INDEX IF NOT EXISTS idx_paymentreceipt_date_shop
ON paymentreceipt (date, shop)
WHERE is_deleted = 0;

-- 复合索引：店铺 + 日期 (备选查询路径)
CREATE INDEX IF NOT EXISTS idx_paymentreceipt_shop_date
ON paymentreceipt (shop, date)
WHERE is_deleted = 0;

-- 单列索引：日期范围查询
CREATE INDEX IF NOT EXISTS idx_paymentreceipt_date
ON paymentreceipt (date)
WHERE is_deleted = 0;

-- 单列索引：店铺查询
CREATE INDEX IF NOT EXISTS idx_paymentreceipt_shop
ON paymentreceipt (shop)
WHERE is_deleted = 0;

-- 3. 用户相关表的索引优化

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users (username) WHERE is_deleted = 0;
CREATE INDEX IF NOT EXISTS idx_users_phone ON users (phone) WHERE is_deleted = 0;
CREATE INDEX IF NOT EXISTS idx_users_status ON users (status) WHERE is_deleted = 0;

-- 角色权限关联表索引
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions (role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions (permission_id);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles (user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles (role_id);

-- 4. 分类表的索引优化

-- 收入分类表索引
CREATE INDEX IF NOT EXISTS idx_income_categories_status 
ON income_categories (status) 
WHERE isdelete = 0;

-- 支出分类表索引
CREATE INDEX IF NOT EXISTS idx_expenditure_categories_status 
ON expenditure_categories (status) 
WHERE isdelete = 0;

-- 5. 店铺表的索引优化

-- 线上店铺表索引
CREATE INDEX IF NOT EXISTS idx_online_stores_status 
ON online_stores (status) 
WHERE isdelete = 0;

-- 线下店铺表索引
CREATE INDEX IF NOT EXISTS idx_offline_stores_status 
ON offline_stores (status) 
WHERE isdelete = 0;

-- 提交事务
COMMIT;

-- 显示创建的索引信息
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
    AND (tablename = 'finance' OR tablename = 'orderlist' OR tablename = 'paymentreceipt')
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- 显示表的统计信息
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 建议：定期执行 ANALYZE 命令来更新表统计信息
-- ANALYZE finance;
-- ANALYZE orderlist;
-- ANALYZE paymentreceipt;

ECHO '索引优化完成！';
ECHO '建议定期执行 ANALYZE 命令来更新表统计信息以保持查询性能。';
