import { requestClient } from '#/api/request';

// 财务记录搜索参数接口
export interface FinanceSearchParams {
  page?: number;
  pageSize?: number;
  date?: string;
  start_date?: string;
  end_date?: string;
  shop?: string;
  type?: string;
  category?: string;
  is_reimbursed?: number;
  reimbursed_status?: number;
}

// 财务记录数据接口
export interface Finance {
  id?: number;
  shop: string;
  record_date: string;
  type: string;
  category: string;
  amount: number;
  description: string;
  screenshot?: string;
  is_reimbursed?: number;
  reimbursed_status?: number;
  reimbursed_at?: string;
  created_at?: string;
  updated_at?: string;
  needReimbursement?: boolean; // 前端使用的字段
}

// 分类数据接口
export interface Category {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 店铺数据接口
export interface Store {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

/**
 * 获取财务记录列表
 */
export async function fetchFinanceList(params: FinanceSearchParams) {
  return requestClient.get<{
    data: Finance[];
    total: number;
    page: number;
    pageSize: number;
  }>('/finance', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 添加财务记录
 */
export async function addFinance(data: Finance) {
  return requestClient.post<{ data: Finance }>('/finance', data);
}

/**
 * 更新财务记录
 */
export async function updateFinance(id: number, data: Finance) {
  return requestClient.put<{ data: Finance }>(`/finance/${id}`, data);
}

/**
 * 删除财务记录
 */
export async function deleteFinance(id: number) {
  return requestClient.delete(`/finance/${id}`);
}

/**
 * 批量删除财务记录
 */
export async function batchDeleteFinance(ids: number[]) {
  return requestClient.post('/finance/batch-delete', { ids });
}

/**
 * 审核报销
 */
export async function auditReimburse(id: number) {
  return requestClient.put(`/finance/reimburse/${id}`);
}

/**
 * 反审核报销
 */
export async function unauditReimburse(id: number) {
  return requestClient.put(`/finance/reimburse/${id}/unaudit`);
}

/**
 * 批量审核报销
 */
export async function batchAuditReimburse(ids: number[]) {
  return requestClient.post('/finance/reimburse/batch-audit', { ids });
}

/**
 * 批量反审核报销
 */
export async function batchUnauditReimburse(ids: number[]) {
  return requestClient.post('/finance/reimburse/batch-unaudit', { ids });
}

/**
 * 获取收入分类列表
 */
export async function fetchIncomeCategories() {
  return requestClient.get<{
    data: Category[];
  }>('/income-categories', {
    responseReturn: 'body',
  });
}

/**
 * 获取支出分类列表
 */
export async function fetchExpenditureCategories() {
  return requestClient.get<{
    data: Category[];
  }>('/expenditure-categories', {
    responseReturn: 'body',
  });
}

/**
 * 获取线下店铺列表
 */
export async function fetchOfflineStores() {
  return requestClient.get<{
    data: Store[];
  }>('/offline-stores', {
    responseReturn: 'body',
  });
}

// 报表相关接口类型定义
export interface ReportSearchParams {
  start_date?: string;
  end_date?: string;
  shop?: string;
  type?: string;
  category?: string;
}

export interface StatisticsData {
  totalIncome: number;
  totalExpenditure: number;
  incomeCount: number;
  expenditureCount: number;
  reimbursedAmount: number;
  unreimbursedAmount: number;
  reimbursedCount: number;
  unreimbursedCount: number;
  netIncome: number;
  totalPaymentAmount: number; // 回款总额
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface DistributionItem {
  name: string;
  value: number;
  count: number;
}

export interface DistributionData {
  incomeExpense: DistributionItem[];
  incomeCategory: DistributionItem[];
  expenseCategory: DistributionItem[];
  netProfitDistribution: DistributionItem[]; // 净利润分布（按店铺）
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface ShopDailyData {
  dates: string[];
  shops: Array<{
    shop: string;
    incomeData: Record<string, number>;
    expenditureData: Record<string, number>;
  }>;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface ShopDetailItem {
  shop: string;
  totalIncome: number;
  totalExpenditure: number;
  incomeCount: number;
  expenditureCount: number;
  recordCount: number;
  reimbursedAmount: number;
  unreimbursedAmount: number;
  reimbursedCount: number;
  unreimbursedCount: number;
  netIncome: number;
  paymentAmount: number; // 回款金额
}

export interface ShopDetailData {
  shopDetails: ShopDetailItem[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

/**
 * 获取财务统计数据
 */
export async function fetchFinanceStatistics(params: ReportSearchParams) {
  return requestClient.get<{
    data: StatisticsData;
  }>('/finance/report/statistics', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取财务分布数据
 */
export async function fetchFinanceDistribution(params: ReportSearchParams) {
  return requestClient.get<{
    data: DistributionData;
  }>('/finance/report/distribution', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取店铺每日数据
 */
export async function fetchShopDailyData(params: ReportSearchParams) {
  return requestClient.get<{
    data: ShopDailyData;
  }>('/finance/report/shop-daily', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取店铺详细数据
 */
export async function fetchShopDetailData(params: ReportSearchParams) {
  return requestClient.get<{
    data: ShopDetailData;
  }>('/finance/report/shop-detail', {
    params,
    responseReturn: 'body',
  });
}
