# 订单日历页面

## 功能概述

订单日历页面使用 Element Plus 的 Calendar 组件，提供直观的日历视图来展示订单数据。

## 主要功能

### 1. 店铺选择
- **全选按钮**: 一键选择/取消选择所有店铺
- **店铺按钮**: 点击单个店铺按钮来切换该店铺的显示状态
- **动态样式**: 选中的店铺按钮会有特殊的视觉效果

### 2. 日历显示
- **订单量**: 显示每日的订单数量
- **销售额**: 显示每日的总销售金额
- **净利润**: 显示每日的净利润（基于回款数据）

### 3. 净利润计算逻辑
- 回款是隔天回的，例如：
  - 6月25日的订单
  - 6月26日回款
  - 登记的回款日期是6月26日
  - 但这笔利润属于6月25日
- 系统会自动将回款日期减1天来计算正确的净利润归属日期

## 技术实现

### 前端组件
- **Vue 3 Composition API**: 使用最新的 Vue 3 语法
- **Element Plus Calendar**: 使用 el-calendar 组件
- **TypeScript**: 完整的类型支持
- **响应式设计**: 支持移动端和桌面端

### API接口
- **GET /api/orders/calendar**: 获取日历数据
  - 参数：
    - `start_date`: 开始日期 (YYYY-MM-DD)
    - `end_date`: 结束日期 (YYYY-MM-DD)
    - `shops[]`: 店铺列表（可选）

### 数据结构
```typescript
interface CalendarDayData {
  date: string;
  orderCount: number;
  totalSales: number;
  netProfit: number;
}

interface CalendarData {
  [shop: string]: CalendarDayData[];
}
```

## 权限控制
- 需要 `MENU_ORDERCALENDAR` 权限才能访问此页面
- 权限在后端API和前端路由中都有验证

## 样式特性
- **现代化设计**: 使用卡片布局和阴影效果
- **颜色编码**: 不同数据类型使用不同颜色
  - 订单量: 绿色
  - 销售额: 蓝色
  - 净利润: 红色
- **响应式布局**: 在不同屏幕尺寸下自适应
- **交互效果**: 按钮悬停和点击效果

## 使用说明

1. **访问页面**: 通过菜单 "订单管理" -> "订单日历" 进入
2. **选择店铺**: 使用顶部的店铺按钮选择要查看的店铺
3. **查看数据**: 在日历格子中查看每日的订单统计
4. **切换月份**: 使用日历组件的导航按钮切换月份

## 问题修复记录

### 修复的问题
1. **店铺获取失败**: 修复了 `Cannot read properties of undefined (reading 'map')` 错误
   - 原因: API响应格式处理不一致
   - 解决: 添加多种响应格式的兼容处理，参考其他组件的实现

2. **权限问题**: 店铺API需要 `MENU_STORE_ONLINE` 权限
   - 原因: 日历页面使用的店铺API需要店铺管理权限
   - 解决: 在订单API中添加专门的店铺选项接口，使用 `MENU_ORDERLIST` 权限

3. **间距问题**: 页面间距过大，不符合系统风格
   - 原因: 没有参考系统其他页面的布局规范
   - 解决: 参考订单列表页面的间距设计，统一布局风格

### 技术改进
- 添加了详细的调试日志，便于问题排查
- 增加了加载状态指示器，提升用户体验
- 添加了空状态提示，当没有店铺时显示友好提示
- 优化了响应式设计，在移动端有更好的显示效果

## 注意事项

- 净利润数据依赖于回款记录，确保回款数据已正确录入
- 日历数据会根据选中的店铺动态更新
- 页面会自动根据当前月份加载数据
- 需要确保用户有 `MENU_ORDERCALENDAR` 权限才能访问此页面
