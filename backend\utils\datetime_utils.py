#!/usr/bin/env python3
"""日期时间工具函数"""

from datetime import datetime
from typing import Any, Optional

def format_datetime_to_iso(dt: Any) -> Optional[str]:
    """
    将datetime对象格式化为ISO 8601字符串（北京时间）

    Args:
        dt: datetime对象、字符串或None

    Returns:
        ISO 8601格式的时间字符串或None
    """
    if dt is None:
        return None

    if isinstance(dt, datetime):
        # 如果datetime对象没有时区信息，假设它是北京时间
        if dt.tzinfo is None:
            # 添加北京时区信息 (+08:00)
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            dt = dt.replace(tzinfo=beijing_tz)
        return dt.isoformat()

    if isinstance(dt, str):
        # 如果已经是字符串，尝试解析后重新格式化
        try:
            parsed_dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            return parsed_dt.isoformat()
        except (ValueError, AttributeError):
            # 如果解析失败，直接返回原字符串
            return dt

    # 其他类型，尝试转换为字符串
    return str(dt)

def format_datetime_fields(data: dict, fields: list = None) -> dict:
    """
    格式化字典中的datetime字段
    
    Args:
        data: 包含datetime字段的字典
        fields: 需要格式化的字段列表，如果为None则格式化常见的时间字段
        
    Returns:
        格式化后的字典
    """
    if fields is None:
        # 默认的时间字段
        fields = ['created_at', 'updated_at', 'last_login', 'reimbursed_at', 'deleted_at']
    
    formatted_data = data.copy()
    
    for field in fields:
        if field in formatted_data:
            formatted_data[field] = format_datetime_to_iso(formatted_data[field])
    
    return formatted_data

def format_datetime_list(data_list: list, fields: list = None) -> list:
    """
    格式化列表中每个字典的datetime字段
    
    Args:
        data_list: 包含字典的列表
        fields: 需要格式化的字段列表
        
    Returns:
        格式化后的列表
    """
    return [format_datetime_fields(item, fields) for item in data_list]
