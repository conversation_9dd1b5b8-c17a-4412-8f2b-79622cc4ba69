<template>
  <div class="calendar-container">
    <!-- 店铺选择区域 -->
    <el-card class="shop-selector">
      <div class="shop-buttons">
        <el-button
          :type="selectedShops.length === allShops.length ? 'primary' : ''"
          @click="toggleAllShops"
          :disabled="allShops.length === 0"
        >
          全选 ({{ allShops.length }})
        </el-button>
        <el-button
          v-for="shop in allShops"
          :key="shop"
          :type="selectedShops.includes(shop) ? 'primary' : ''"
          @click="toggleShop(shop)"
        >
          {{ shop }}
        </el-button>
        <div v-if="allShops.length === 0" class="no-shops">
          暂无店铺数据，请先添加店铺
        </div>
      </div>
    </el-card>

    <!-- 日历区域 -->
    <el-card class="calendar-wrapper" v-loading="loading" element-loading-text="加载日历数据...">
      <el-calendar v-model="currentDate" class="order-calendar">
        <template #date-cell="{ data }">
          <div class="calendar-cell">
            <div class="date-number">{{ data.day.split('-').pop() }}</div>
            <div v-if="getDayData(data.day)" class="day-stats">
              <div class="stat-item orders">
                <span class="label">订单</span>
                <span class="value">{{ getDayData(data.day)?.orderCount || 0 }}</span>
              </div>
              <div class="stat-item sales">
                <span class="label">销售</span>
                <span class="value">¥{{ (getDayData(data.day)?.totalSales || 0).toFixed(2) }}</span>
              </div>
              <div class="stat-item profit" :class="{ 'positive': (getDayData(data.day)?.netProfit || 0) > 0, 'negative': (getDayData(data.day)?.netProfit || 0) < 0 }">
                <span class="label">净利</span>
                <span class="value">¥{{ (getDayData(data.day)?.netProfit || 0).toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { ElCalendar, ElButton, ElMessage } from 'element-plus';
import { fetchCalendarData, type CalendarData, type CalendarDayData } from '#/api/order';
import { fetchOnlineStoreOptions } from '#/api/order';
import dayjs from 'dayjs';

defineOptions({ name: 'OrderCalendar' });

// 响应式数据
const currentDate = ref(new Date());
const allShops = ref<string[]>([]);
const selectedShops = ref<string[]>([]);
const calendarData = ref<CalendarData>({});
const loading = ref(false);

// 计算属性：当前月份的开始和结束日期
const currentMonthRange = computed(() => {
  const start = dayjs(currentDate.value).startOf('month').format('YYYY-MM-DD');
  const end = dayjs(currentDate.value).endOf('month').format('YYYY-MM-DD');
  return { start, end };
});

// 获取指定日期的数据
const getDayData = (date: string): CalendarDayData | null => {
  if (selectedShops.value.length === 0) return null;

  // 合并所有选中店铺的数据
  let totalOrderCount = 0;
  let totalSales = 0;
  let totalNetProfit = 0;

  selectedShops.value.forEach(shop => {
    const shopData = calendarData.value[shop];
    if (shopData) {
      const dayData = shopData.find(item => item.date === date);
      if (dayData) {
        totalOrderCount += dayData.orderCount;
        totalSales += dayData.totalSales;
        totalNetProfit += dayData.netProfit;
      }
    }
  });

  // 移除调试信息，避免重复输出
  // if (totalOrderCount > 0 || totalSales > 0 || totalNetProfit > 0) {
  //   console.log(`日期 ${date} 的数据:`, {
  //     orderCount: totalOrderCount,
  //     totalSales,
  //     netProfit: totalNetProfit
  //   });
  // }

  if (totalOrderCount === 0 && totalSales === 0 && totalNetProfit === 0) {
    return null;
  }

  return {
    date,
    orderCount: totalOrderCount,
    totalSales,
    netProfit: totalNetProfit
  };
};

// 移除formatNumber函数，直接使用toFixed(2)显示金额

// 切换店铺选择
const toggleShop = (shop: string) => {
  const index = selectedShops.value.indexOf(shop);
  if (index > -1) {
    selectedShops.value.splice(index, 1);
  } else {
    selectedShops.value.push(shop);
  }
};

// 全选/取消全选
const toggleAllShops = () => {
  if (selectedShops.value.length === allShops.value.length) {
    selectedShops.value = [];
  } else {
    selectedShops.value = [...allShops.value];
  }
};

// 获取店铺列表
const fetchShops = async () => {
  try {
    const response = await fetchOnlineStoreOptions();
    //console.log('店铺API响应:', response);

    // 尝试多种数据格式，参考其他组件的处理方式
    let storeData = null;
    if (Array.isArray(response)) {
      storeData = response;
    } else if (response && Array.isArray(response.data)) {
      storeData = response.data;
    }

    if (storeData) {
      // 只显示启用的店铺
      const filteredStores = storeData.filter(store => store.status === 1);
      allShops.value = filteredStores.map(shop => shop.name);
      // 默认选择所有店铺
      selectedShops.value = [...allShops.value];
      //console.log('获取到的店铺:', allShops.value);
    } else {
      console.error('店铺数据格式错误:', response);
      ElMessage.error('店铺数据格式错误');
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error);
    ElMessage.error('获取店铺列表失败');
  }
};

// 获取日历数据
const fetchData = async () => {
  // 暂时移除店铺筛选，先测试基本功能
  // if (selectedShops.value.length === 0) {
  //   calendarData.value = {};
  //   return;
  // }

  loading.value = true;
  try {
    const response = await fetchCalendarData({
      start_date: currentMonthRange.value.start,
      end_date: currentMonthRange.value.end,
      shops: selectedShops.value.length > 0 ? selectedShops.value : undefined
    });

    calendarData.value = response.data || {};

  } catch (error: any) {
    ElMessage.error('获取日历数据失败');
  } finally {
    loading.value = false;
  }
};

// 监听日期变化
watch(currentDate, () => {
  fetchData();
});

// 监听选中店铺变化
watch(selectedShops, () => {
  fetchData();
}, { deep: true });

// 组件挂载时初始化
onMounted(async () => {
  await fetchShops();
  await fetchData();
});

</script>

<style scoped lang="scss">
.calendar-container {
  padding: 16px;
  height: calc(100vh - 90px); /* 减去tab栏和header的高度 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.shop-selector {
  background: white;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;

  .shop-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .el-button {
      
      transition: all 0.3s ease;
      font-size: 12px;
      padding: 8px 12px;

      &.active {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .no-shops {
    color: #909399;
    font-size: 14px;
    padding: 8px 12px;
    text-align: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px dashed #dcdfe6;
  }
}

.calendar-wrapper {
  background: white;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow: hidden;

  :deep(.el-calendar) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-calendar__body {
      flex: 1;
      overflow: auto;
    }
  }
}

.calendar-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .date-number {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 6px;
    text-align: center;
  }

  .day-stats {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
    font-size: 10px;

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1px 4px;
      border-radius: 3px;
      background-color: rgba(0, 0, 0, 0.05);

      .label {
        color: #606266;
        font-weight: 500;
        font-size: 9px;
        min-width: 20px;
      }

      .value {
        font-weight: 600;
      }

      &.orders {
        background-color: rgba(103, 194, 58, 0.1);
        .value {
          color: #67c23a;
        }
      }

      &.sales {
        background-color: rgba(64, 158, 255, 0.1);
        .value {
          color: #409eff;
        }
      }

      &.profit {
        background-color: rgba(144, 147, 153, 0.1);
        .value {
          color: #909399;
        }

        &.positive {
          background-color: rgba(103, 194, 58, 0.1);
          .value {
            color: #67c23a;
          }
        }

        &.negative {
          background-color: rgba(245, 108, 108, 0.1);
          .value {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .calendar-container {
    padding: 8px;
    gap: 8px;
  }

  .shop-selector {
    padding: 12px;

    .shop-buttons {
      gap: 6px;

      .el-button {
        font-size: 11px;
        padding: 6px 10px;
      }
    }
  }

  .calendar-wrapper {
    padding: 12px;
  }

  .calendar-cell {
    .date-number {
      font-size: 12px;
      margin-bottom: 4px;
    }

    .day-stats {
      font-size: 9px;
      gap: 2px;

      .stat-item {
        padding: 1px 3px;
      }
    }
  }
}
</style>
