import { requestClient } from './request';

/**
 * 权限管理器相关API
 */

/** 添加菜单权限请求参数 */
export interface AddMenuPermissionParams {
  /** 权限码 */
  code: string;
  /** 权限名称 */
  name: string;
  /** 权限描述 */
  description: string;
  /** 父权限码 */
  parent_code?: string;
}

/** 添加按钮权限请求参数 */
export interface AddButtonPermissionParams {
  /** 权限码 */
  code: string;
  /** 权限名称 */
  name: string;
  /** 权限描述 */
  description: string;
  /** 父权限码 */
  parent_code: string;
}

/** 批量添加模块权限请求参数 */
export interface AddModulePermissionsParams {
  /** 模块名称 */
  module_name: string;
  /** 模块代码 */
  module_code: string;
  /** 父权限码 */
  parent_code?: string;
  /** 按钮权限列表 */
  buttons?: string[];
}

/** 权限信息 */
export interface PermissionItem {
  /** 权限ID */
  id: number;
  /** 权限码 */
  code: string;
  /** 权限名称 */
  name: string;
  /** 权限类型 */
  type: 'menu' | 'button';
  /** 权限描述 */
  description: string;
  /** 父权限码 */
  parent_code?: string;
}

/** 批量添加结果 */
export interface BatchAddResult {
  /** 详细结果 */
  results: Array<{
    success: boolean;
    message: string;
    permission_id?: number;
  }>;
  /** 成功数量 */
  success_count: number;
  /** 总数量 */
  total_count: number;
}

/** 为角色分配权限请求参数 */
export interface AssignPermissionsToRoleParams {
  /** 角色ID */
  role_id: number;
  /** 权限码列表 */
  permission_codes: string[];
}

/**
 * 添加菜单权限
 */
export function addMenuPermission(data: AddMenuPermissionParams) {
  return requestClient.post('/permission-manager/menu', data);
}

/**
 * 添加按钮权限
 */
export function addButtonPermission(data: AddButtonPermissionParams) {
  return requestClient.post('/permission-manager/button', data);
}

/**
 * 批量添加模块权限
 */
export function addModulePermissions(data: AddModulePermissionsParams) {
  return requestClient.post<BatchAddResult>('/permission-manager/module', data);
}

/**
 * 获取权限列表
 * @param type 权限类型 menu | button | undefined(全部)
 */
export function fetchPermissionsList(type?: 'menu' | 'button') {
  return requestClient.get<PermissionItem[]>('/permission-manager/list', {
    params: type ? { type } : {}
  });
}

/**
 * 删除权限
 * @param code 权限码
 */
export function deletePermission(code: string) {
  return requestClient.delete(`/permission-manager/delete/${code}`);
}

/**
 * 为角色分配权限
 */
export function assignPermissionsToRole(data: AssignPermissionsToRoleParams) {
  return requestClient.post('/permission-manager/assign-role', data);
}
