<template>
  <div class="backup-list">
    <div class="list-header">
      <h3>备份记录</h3>
      <ElButton @click="refreshList" :loading="loading" size="small">
        <ElIcon><Refresh /></ElIcon>
        刷新
      </ElButton>
    </div>

    <div v-if="loading" class="loading-container">
      <ElSkeleton :rows="3" animated />
    </div>

    <div v-else-if="backups.length === 0" class="empty-container">
      <ElEmpty description="暂无备份记录" />
    </div>

    <div v-else class="backup-items">
      <div
        v-for="backup in backups"
        :key="backup.filename"
        class="backup-item"
        :class="{ 'backup-failed': backup.status === 'failed' }"
      >
        <div class="backup-info">
          <div class="backup-name">{{ backup.backup_name }}</div>
          <div class="backup-meta">
            <span class="backup-type">{{ backup.backup_type === 'full' ? '完整备份' : '部分备份' }}</span>
            <span class="backup-size">{{ backup.size }}</span>
            <span class="backup-time">{{ formatTime(backup.created_at) }}</span>
          </div>
          <div v-if="backup.backup_type === 'partial' && backup.tables.length > 0" class="backup-tables">
            包含表: {{ backup.tables.join(', ') }}
          </div>
          <div v-if="backup.status === 'failed' && backup.error" class="backup-error">
            错误: {{ backup.error }}
          </div>
        </div>

        <div class="backup-actions">
          <ElButton
            v-if="backup.status === 'completed'"
            size="small"
            type="primary"
            @click="handleDownload(backup.filename)"
          >
            <ElIcon><Download /></ElIcon>
            下载
          </ElButton>
          <ElButton
            size="small"
            type="danger"
            @click="handleDelete(backup)"
          >
            <ElIcon><Delete /></ElIcon>
            删除
          </ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  ElMessage,
  ElMessageBox,
  ElIcon,
  ElButton,
  ElSkeleton,
  ElEmpty
} from 'element-plus';
import { Refresh, Download, Delete } from '@element-plus/icons-vue';
import { fetchBackupList, downloadBackup, deleteBackup, type BackupRecord } from '#/api/backup';

interface Props {
  autoRefresh?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: false
});

const emit = defineEmits<{
  refresh: [];
}>();

// 数据
const backups = ref<BackupRecord[]>([]);
const loading = ref(false);

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-';
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return timeStr;
  }
};

// 加载备份列表
const loadBackups = async () => {
  loading.value = true;
  try {
    const result = await fetchBackupList();
    // 由于 requestClient 的 responseReturn: 'body' 配置，
    // 成功的响应会直接返回数据数组
    if (Array.isArray(result)) {
      backups.value = result;
    } else {
      backups.value = [];
      ElMessage.warning('获取备份列表失败');
    }
  } catch (error) {
    console.error('加载备份列表失败:', error);
    ElMessage.error('加载备份列表失败');
    backups.value = [];
  } finally {
    loading.value = false;
  }
};

// 刷新列表
const refreshList = async () => {
  await loadBackups();
  emit('refresh');
};



// 下载备份
const handleDownload = async (filename: string) => {
  try {
    ElMessage.info('正在准备下载...');
    const result = await downloadBackup(filename);
    if (result && result.success) {
      ElMessage.success({
        message: `文件 "${result.filename}" 下载成功！请检查浏览器下载目录`,
        duration: 5000
      });
    }
  } catch (error: any) {
    console.error('下载备份失败:', error);
    ElMessage.error(error.message || '下载备份失败');
  }
};

// 删除备份
const handleDelete = async (backup: BackupRecord) => {
  try {
    await ElMessageBox.confirm(
      `确认删除备份 "${backup.backup_name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await deleteBackup(backup.filename);
    ElMessage.success('备份删除成功');
    await refreshList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error);
      ElMessage.error('删除备份失败');
    }
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadBackups();
});

// 暴露刷新方法
defineExpose({
  refreshList
});
</script>

<style scoped>
.backup-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.backup-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.2s;
}

.backup-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.backup-item.backup-failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.backup-info {
  flex: 1;
}

.backup-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.backup-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.backup-type {
  background: #e1f3d8;
  color: #67c23a;
  padding: 2px 6px;
  border-radius: 3px;
}

.backup-tables {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
}

.backup-error {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
}

.backup-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}
</style>
