#!/usr/bin/env python3
"""备份管理API"""

from flask import Blueprint, request, jsonify, send_file
import os
from .auth import token_required, check_permission
from .utils import format_response
from utils.backup_utils import backup_database, list_backups, delete_backup, restore_database, format_file_size

backup_bp = Blueprint('backup', __name__, url_prefix='/api')

@backup_bp.route('/backup/create', methods=['POST'])
@token_required
@check_permission('MENU_BACKUP')
def create_backup():
    """创建数据库备份"""
    try:
        data = request.get_json()

        # 验证必需字段
        if not data or 'name' not in data or 'type' not in data:
            return format_response(
                success=False,
                message="缺少必需的参数：name 和 type"
            )

        backup_info = {
            'name': data['name'],
            'type': data['type'],  # 'full' 或 'partial'
            'format': 'custom',  # 固定使用custom格式
            'tables': data.get('tables', [])
        }

        # 验证备份类型
        if backup_info['type'] not in ['full', 'partial']:
            return format_response(
                success=False,
                message="备份类型必须是 'full' 或 'partial'"
            )

        # 如果是部分备份，验证表列表
        if backup_info['type'] == 'partial':
            if not backup_info['tables']:
                return format_response(
                    success=False,
                    message="部分备份必须指定要备份的表"
                )

        # 执行备份
        result = backup_database(backup_info)

        if result['success']:
            return format_response(
                success=True,
                data={
                    'backup_name': backup_info['name'],
                    'backup_type': backup_info['type'],
                    'file_size': format_file_size(result['file_size']),
                    'file_path': result['backup_path']
                },
                message=result['message']
            )
        else:
            return format_response(
                success=False,
                message=result['message'],
                error=result.get('error')
            )

    except Exception as e:
        return format_response(
            success=False,
            message=f"创建备份失败: {str(e)}"
        )

@backup_bp.route('/backup/restore', methods=['POST'])
@token_required
@check_permission('MENU_BACKUP')
def restore_backup():
    """恢复数据库备份"""
    try:
        data = request.get_json()

        # 验证必需字段
        if not data or 'filename' not in data:
            return format_response(
                success=False,
                message="缺少必需的参数：filename"
            )

        restore_info = {
            'filename': data['filename']
        }

        # 验证文件名 - 只支持.backup格式
        filename = restore_info['filename']
        if not filename.endswith('.backup'):
            return format_response(
                success=False,
                message="无效的备份文件格式，只支持.backup格式"
            )

        # 执行恢复
        result = restore_database(restore_info)

        if result['success']:
            return format_response(
                success=True,
                data={
                    'filename': filename,
                    'output': result.get('output', ''),
                    'warnings': result.get('warnings', '')
                },
                message=result['message']
            )
        else:
            return format_response(
                success=False,
                message=result['message'],
                error=result.get('error')
            )

    except Exception as e:
        return format_response(
            success=False,
            message=f"恢复备份失败: {str(e)}"
        )







@backup_bp.route('/backup/list', methods=['GET'])
@token_required
@check_permission('MENU_BACKUP')
def get_backup_list():
    """获取备份文件列表"""
    try:
        backups = list_backups()
        
        # 格式化备份信息
        formatted_backups = []
        for backup in backups:
            formatted_backup = {
                'filename': backup['filename'],
                'backup_name': backup.get('backup_name', backup['filename'].replace('.sql', '')),
                'backup_type': backup.get('backup_type', 'unknown'),
                'tables': backup.get('tables', []),
                'size': format_file_size(backup['size']),
                'size_bytes': backup['size'],
                'status': backup.get('status', 'completed'),
                'created_at': backup.get('created_at', backup.get('modified_at')),
                'error': backup.get('error')
            }
            formatted_backups.append(formatted_backup)
        
        return format_response(
            success=True,
            data=formatted_backups,
            message="获取备份列表成功"
        )
        
    except Exception as e:
        return format_response(
            success=False,
            message=f"获取备份列表失败: {str(e)}"
        )

@backup_bp.route('/backup/download/<filename>', methods=['GET'])
@token_required
@check_permission('MENU_BACKUP')
def download_backup(filename):
    """下载备份文件"""
    try:
        from utils.backup_utils import get_backup_dir
        
        backup_dir = get_backup_dir()
        backup_path = os.path.join(backup_dir, filename)
        
        # 验证文件存在且是.backup文件
        if not os.path.exists(backup_path) or not filename.endswith('.backup'):
            return format_response(
                success=False,
                message="备份文件不存在或格式不正确"
            )
        
        # 固定使用.backup格式的MIME类型
        return send_file(
            backup_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        return format_response(
            success=False,
            message=f"下载备份文件失败: {str(e)}"
        )

@backup_bp.route('/backup/delete/<filename>', methods=['DELETE'])
@token_required
@check_permission('MENU_BACKUP')
def delete_backup_file(filename):
    """删除备份文件"""
    try:
        # 验证文件名 - 只支持.backup格式
        if not filename.endswith('.backup'):
            return format_response(
                success=False,
                message="无效的备份文件名，只支持.backup格式"
            )
        
        result = delete_backup(filename)
        
        if result['success']:
            return format_response(
                success=True,
                message=result['message']
            )
        else:
            return format_response(
                success=False,
                message=result['message'],
                error=result.get('error')
            )
            
    except Exception as e:
        return format_response(
            success=False,
            message=f"删除备份文件失败: {str(e)}"
        )

@backup_bp.route('/backup/info/<filename>', methods=['GET'])
@token_required
@check_permission('MENU_BACKUP')
def get_backup_info(filename):
    """获取备份文件详细信息"""
    try:
        backups = list_backups()
        backup = next((b for b in backups if b['filename'] == filename), None)
        
        if not backup:
            return format_response(
                success=False,
                message="备份文件不存在"
            )
        
        backup_info = {
            'filename': backup['filename'],
            'backup_name': backup.get('backup_name', backup['filename'].replace('.sql', '')),
            'backup_type': backup.get('backup_type', 'unknown'),
            'tables': backup.get('tables', []),
            'size': format_file_size(backup['size']),
            'size_bytes': backup['size'],
            'status': backup.get('status', 'completed'),
            'created_at': backup.get('created_at'),
            'completed_at': backup.get('completed_at'),
            'error': backup.get('error')
        }
        
        return format_response(
            success=True,
            data=backup_info,
            message="获取备份信息成功"
        )
        
    except Exception as e:
        return format_response(
            success=False,
            message=f"获取备份信息失败: {str(e)}"
        )
