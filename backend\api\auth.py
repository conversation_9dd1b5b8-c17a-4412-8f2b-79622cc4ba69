from flask import Blueprint, request, g
from models.user import User
from models.permission import Permission
from utils.jwt_utils import JWTUtils
from utils.response import ResponseUtils
from functools import wraps

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return ResponseUtils.unauthorized("令牌格式错误")

        if not token:
            return ResponseUtils.unauthorized("缺少访问令牌")

        # 验证令牌
        payload = JWTUtils.verify_access_token(token)
        if not payload:
            return ResponseUtils.unauthorized("无效的访问令牌")

        # 将用户信息存储到g对象中
        g.current_user = payload
        return f(*args, **kwargs)

    return decorated

def check_permission(permission_code):
    """权限检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            # 检查用户是否已登录
            if not hasattr(g, 'current_user') or not g.current_user:
                return ResponseUtils.unauthorized("用户未登录")

            user_id = g.current_user.get('user_id')
            if not user_id:
                return ResponseUtils.unauthorized("无效的用户信息")

            # 检查用户权限
            user = User()
            if not user.has_permission(user_id, permission_code):
                return ResponseUtils.forbidden("权限不足")

            return f(*args, **kwargs)
        return decorated
    return decorator

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录接口"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return ResponseUtils.error("请求数据不能为空", 400)
        
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        # 验证必填字段
        if not username or not password:
            return ResponseUtils.error("用户名和密码不能为空", 400)
        
        # 查找用户
        user_model = User()
        user = user_model.find_by_username_or_phone(username)
        
        if not user:
            return ResponseUtils.error("用户名或密码错误", 401)
        
        # 验证密码
        if not user_model.verify_password(password, user['password']):
            return ResponseUtils.error("用户名或密码错误", 401)
        
        # 更新最后登录时间
        user_model.update_last_login(user['id'])
        
        # 生成JWT令牌
        access_token = JWTUtils.generate_access_token(user)
        refresh_token = JWTUtils.generate_refresh_token(user)
        
        # 获取用户的角色信息
        user_model = User()
        user_roles = user_model.get_user_role_names(user['id'])

        # 如果没有分配角色，使用旧的role字段作为兼容
        if not user_roles and user['role']:
            user_roles = [user['role']]

        # 准备返回的用户信息（不包含密码）
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'phone': user['phone'],
            'role': user['role'],  # 保持兼容性
            'realName': user['username'],  # 前端需要的字段
            'roles': user_roles,  # 前端需要的字段格式
            'accessToken': access_token
        }
        
        # 设置刷新令牌到Cookie（可选）
        response = ResponseUtils.success(user_info, "登录成功")
        # response[0].set_cookie('refresh_token', refresh_token, httponly=True, secure=False)
        
        return response
        
    except Exception as e:
        print(f"登录错误: {str(e)}")
        return ResponseUtils.error("登录失败，请稍后重试", 500)

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出接口"""
    try:
        # 这里可以实现令牌黑名单等逻辑
        response = ResponseUtils.success(None, "登出成功")
        # response[0].set_cookie('refresh_token', '', expires=0)
        return response
    except Exception as e:
        print(f"登出错误: {str(e)}")
        return ResponseUtils.error("登出失败", 500)

@auth_bp.route('/permissions', methods=['GET'])
@token_required
def get_user_permissions():
    """获取当前用户权限"""
    try:
        current_user_id = g.current_user['user_id']
        user_model = User()
        permissions = user_model.get_user_permissions(current_user_id)

        return ResponseUtils.success(permissions, "获取用户权限成功")
    except Exception as e:
        print(f"获取用户权限错误: {str(e)}")
        return ResponseUtils.error("获取用户权限失败", 500)

@auth_bp.route('/refresh', methods=['POST'])
def refresh():
    """刷新令牌接口"""
    try:
        # 这里可以实现刷新令牌的逻辑
        return ResponseUtils.success(None, "令牌刷新成功")
    except Exception as e:
        print(f"刷新令牌错误: {str(e)}")
        return ResponseUtils.error("刷新令牌失败", 500)

@auth_bp.route('/codes', methods=['GET'])
@token_required
def get_access_codes():
    """获取用户权限码接口"""
    try:
        user_id = g.current_user['user_id']

        # 使用RBAC系统获取用户权限码
        permission_model = Permission()
        codes = permission_model.get_user_permission_codes(user_id)

        # 如果没有权限码，返回空数组
        if not codes:
            codes = []

        return ResponseUtils.success(codes, "获取权限码成功")

    except Exception as e:
        print(f"获取权限码错误: {str(e)}")
        return ResponseUtils.error("获取权限码失败", 500)

@auth_bp.route('/user-info', methods=['GET'])
@token_required
def get_user_info():
    """获取用户信息接口"""
    try:
        user_id = g.current_user['id']

        # 从数据库获取最新的用户信息
        user_model = User()
        user = user_model.get_user_by_id(user_id)

        if not user:
            return ResponseUtils.not_found("用户不存在")

        # 获取用户的角色信息
        user_roles = user_model.get_user_role_names(user['id'])

        # 如果没有分配角色，使用旧的role字段作为兼容
        if not user_roles and user['role']:
            user_roles = [user['role']]

        # 准备返回的用户信息
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'phone': user['phone'],
            'role': user['role'],  # 保持兼容性
            'realName': user['username'],  # 前端需要的字段
            'roles': user_roles,  # 前端需要的字段格式
            'status': user['status'],
            'last_login': user['last_login'],
            'created_at': user['created_at']
        }

        return ResponseUtils.success(user_info, "获取用户信息成功")

    except Exception as e:
        print(f"获取用户信息错误: {str(e)}")
        return ResponseUtils.error("获取用户信息失败", 500)
