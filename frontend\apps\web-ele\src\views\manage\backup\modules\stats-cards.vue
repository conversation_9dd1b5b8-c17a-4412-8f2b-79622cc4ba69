<template>
  <ElRow :gutter="10" class="stats-row">
    <ElCol :span="8">
      <ElCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon system">
            <ElIcon><DataBoard /></ElIcon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ tableCount || 0 }}</div>
            <div class="stat-label">数据表总数</div>
          </div>
        </div>
      </ElCard>
    </ElCol>
    <ElCol :span="8">
      <ElCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon business">
            <ElIcon><Document /></ElIcon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ (totalRecords || 0).toLocaleString() }}</div>
            <div class="stat-label">记录总数</div>
          </div>
        </div>
      </ElCard>
    </ElCol>
    <ElCol :span="8">
      <ElCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon config">
            <ElIcon><InfoFilled /></ElIcon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalSize || '0 KB' }}</div>
            <div class="stat-label">数据大小</div>
          </div>
        </div>
      </ElCard>
    </ElCol>
  </ElRow>
</template>

<script setup lang="ts">
import {
  ElCard,
  ElRow,
  ElCol,
  ElIcon
} from 'element-plus';
import {
  DataBoard,
  Document,
  SuccessFilled,
  InfoFilled
} from '@element-plus/icons-vue';

interface Props {
  tableCount: number;
  totalRecords: number;
  totalSize: string;
  backupCount: number;
}

defineProps<Props>();
</script>

<style scoped>
/* 统计卡片 */
.stats-row {
  flex-shrink: 0;
}

.stat-card {
  height: 90px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 6px 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.system {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-icon.business {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon.config {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

@media (max-width: 768px) {
  .stat-card {
    height: 70px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 6px 8px;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 6px;
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}
</style>
