import { requestClient } from '#/api/request';

// 订单报表搜索参数接口
export interface OrderReportSearchParams {
  start_date?: string;
  end_date?: string;
  shop?: string;
}

// 订单统计数据接口
export interface OrderStatisticsData {
  totalOrders: number;
  totalSales: number;
  productProfit: number;       // 商品利润
  totalBoxFee: number;         // 总餐盒费
  totalDeliveryFee: number;    // 总配送费
  totalProfit: number;
  totalPurchasePrice: number;  // 总买入价
  totalPaymentAmount: number;  // 总回款金额
  avgOrderValue: number;       // 平均客单价
  avgProfit: number;           // 平均利润
  actualProfitMargin: number;  // 实际利润率
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// 订单分布数据接口
export interface OrderDistributionData {
  shopDistribution: Array<{
    name: string;
    value: number;
    count: number;
  }>;
  profitDistribution: Array<{
    name: string;
    value: number;
    count: number;
  }>;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// 订单每日数据接口
export interface OrderDailyData {
  dates: string[];
  shops: Array<{
    shop: string;
    salesData: Record<string, number>;
    profitData: Record<string, number>;
    orderCountData: Record<string, number>;
  }>;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// 订单详细统计项接口
export interface OrderDetailItem {
  shop: string;
  orderCount: number;
  totalSales: number;
  totalProfit: number;
  totalActualPayment: number;
  totalPurchasePrice: number;
  totalPaymentAmount: number;
  avgOrderValue: number;
  avgProfit: number;
  profitMargin: number;
}

// 订单详细数据接口
export interface OrderDetailData {
  shopDetails: OrderDetailItem[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

/**
 * 获取订单统计数据
 */
export async function fetchOrderStatistics(params: OrderReportSearchParams) {
  return requestClient.get<{
    data: OrderStatisticsData;
  }>('/orders/report/statistics', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取订单分布数据
 */
export async function fetchOrderDistribution(params: OrderReportSearchParams) {
  return requestClient.get<{
    data: OrderDistributionData;
  }>('/orders/report/distribution', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取订单每日数据
 */
export async function fetchOrderDailyData(params: OrderReportSearchParams) {
  return requestClient.get<{
    data: OrderDailyData;
  }>('/orders/report/daily', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 获取订单详细数据
 */
export async function fetchOrderDetailData(params: OrderReportSearchParams) {
  return requestClient.get<{
    data: OrderDetailData;
  }>('/orders/report/detail', {
    params,
    responseReturn: 'body',
  });
}
