#!/usr/bin/env python3
"""数据库备份工具"""

import os
import subprocess
import json
from datetime import datetime
from typing import List, Dict, Any
import psycopg2
from config.config import Config

def get_backup_dir():
    """获取备份目录路径"""
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    backup_dir = os.path.join(current_dir, 'storage', 'backup')
    
    # 确保备份目录存在
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def generate_backup_filename(backup_name: str, backup_type: str) -> str:
    """生成备份文件名"""
    # 清理文件名中的特殊字符
    safe_name = "".join(c for c in backup_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_name = safe_name.replace(' ', '_')

    # 固定使用.backup扩展名
    return f"{safe_name}.backup"

def create_backup_metadata(backup_info: Dict[str, Any]) -> Dict[str, Any]:
    """创建备份元数据"""
    return {
        'backup_name': backup_info['name'],
        'backup_type': backup_info['type'],
        'tables': backup_info['tables'],
        'created_at': datetime.now().isoformat(),
        'file_size': 0,  # 将在备份完成后更新
        'status': 'in_progress'
    }

def backup_database(backup_info: Dict[str, Any]) -> Dict[str, Any]:
    """执行数据库备份"""
    try:
        backup_dir = get_backup_dir()
        filename = generate_backup_filename(backup_info['name'], backup_info['type'])
        backup_path = os.path.join(backup_dir, filename)
        metadata_path = os.path.join(backup_dir, f"{filename}.meta")

        # 创建元数据
        metadata = create_backup_metadata(backup_info)

        # 获取数据库配置
        db_config = Config.DATABASE_CONFIG
        
        # 构建 pg_dump 命令 - 固定使用custom格式
        cmd = [
            'pg_dump',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}",
            f"--username={db_config['user']}",
            f"--dbname={db_config['database']}",
            '--no-password',
            '--verbose',
            '--no-owner',  # 不包含所有者信息
            '--no-privileges',  # 不包含权限信息
            '--format=custom',  # 固定使用custom格式
            '--compress=9',  # 最高压缩级别
            f"--file={backup_path}"
        ]
        
        # 如果是部分备份，只备份指定的表
        if backup_info['type'] == 'partial' and backup_info['tables']:
            for table in backup_info['tables']:
                cmd.extend(['--table', table])
        
        # 设置环境变量（PostgreSQL密码和编码）
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']
        env['PGCLIENTENCODING'] = 'UTF8'
        env['LC_ALL'] = 'C.UTF-8'

        # 执行备份
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            # 备份成功，更新元数据
            file_size = os.path.getsize(backup_path) if os.path.exists(backup_path) else 0
            metadata.update({
                'status': 'completed',
                'file_size': file_size,
                'completed_at': datetime.now().isoformat()
            })
            
            # 保存元数据
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'message': '备份创建成功',
                'backup_path': backup_path,
                'file_size': file_size,
                'metadata': metadata
            }
        else:
            # 备份失败
            error_msg = result.stderr or result.stdout or '未知错误'
            metadata.update({
                'status': 'failed',
                'error': error_msg,
                'failed_at': datetime.now().isoformat()
            })
            
            # 保存失败的元数据
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # 清理失败的备份文件
            if os.path.exists(backup_path):
                os.remove(backup_path)
            
            return {
                'success': False,
                'message': f'备份创建失败: {error_msg}',
                'error': error_msg
            }
            
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'message': '备份超时，请检查数据库连接或数据量大小',
            'error': 'Backup timeout'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'备份过程中发生错误: {str(e)}',
            'error': str(e)
        }

def list_backups() -> List[Dict[str, Any]]:
    """列出所有备份文件"""
    try:
        backup_dir = get_backup_dir()
        backups = []
        
        # 遍历备份目录 - 只显示.backup文件
        for filename in os.listdir(backup_dir):
            if filename.endswith('.backup'):
                backup_path = os.path.join(backup_dir, filename)
                metadata_path = os.path.join(backup_dir, f"{filename}.meta")
                
                # 获取文件信息
                stat = os.stat(backup_path)
                
                backup_info = {
                    'filename': filename,
                    'path': backup_path,
                    'size': stat.st_size,
                    'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
                
                # 如果有元数据文件，读取详细信息
                if os.path.exists(metadata_path):
                    try:
                        with open(metadata_path, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                            backup_info.update(metadata)
                    except Exception as e:
                        pass  # 忽略元数据读取失败
                
                backups.append(backup_info)
        
        # 按创建时间倒序排列
        backups.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        return backups
        
    except Exception as e:
        return []

def delete_backup(filename: str) -> Dict[str, Any]:
    """删除备份文件"""
    try:
        backup_dir = get_backup_dir()
        backup_path = os.path.join(backup_dir, filename)
        metadata_path = os.path.join(backup_dir, f"{filename}.meta")
        
        # 删除备份文件
        if os.path.exists(backup_path):
            os.remove(backup_path)
        
        # 删除元数据文件
        if os.path.exists(metadata_path):
            os.remove(metadata_path)
        
        return {
            'success': True,
            'message': '备份文件删除成功'
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'删除备份文件失败: {str(e)}',
            'error': str(e)
        }

def test_pg_restore_list(backup_path: str) -> Dict[str, Any]:
    """测试pg_restore --list命令"""
    try:
        cmd = ['pg_restore', '--list', backup_path]

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=30
        )

        return {
            'success': result.returncode == 0,
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'command': ' '.join(cmd)
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'command': ' '.join(cmd) if 'cmd' in locals() else 'unknown'
        }

def test_database_connection() -> Dict[str, Any]:
    """测试数据库连接"""
    try:
        db_config = Config.DATABASE_CONFIG

        # 测试连接
        cmd = [
            'psql',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}",
            f"--username={db_config['user']}",
            f"--dbname={db_config['database']}",
            '--no-password',
            '--command', 'SELECT 1;'
        ]

        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']

        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=30
        )

        return {
            'success': result.returncode == 0,
            'message': 'Database connection test',
            'output': result.stdout,
            'error': result.stderr
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Connection test failed: {str(e)}',
            'error': str(e)
        }



def restore_database(restore_info: Dict[str, Any]) -> Dict[str, Any]:
    """执行数据库恢复"""
    try:
        backup_dir = get_backup_dir()
        filename = restore_info['filename']
        backup_path = os.path.join(backup_dir, filename)

        # 检查备份文件是否存在
        if not os.path.exists(backup_path):
            return {
                'success': False,
                'message': f'备份文件不存在: {filename}',
                'error': 'File not found'
            }

        # 测试数据库连接
        conn_test = test_database_connection()
        if not conn_test['success']:
            return {
                'success': False,
                'message': f'数据库连接失败: {conn_test["error"]}',
                'error': conn_test['error']
            }

        # 测试备份文件是否有效
        list_test = test_pg_restore_list(backup_path)
        if not list_test['success']:
            return {
                'success': False,
                'message': f'备份文件无效或损坏: {list_test.get("stderr", list_test.get("error", "未知错误"))}',
                'error': list_test.get('stderr', list_test.get('error', '未知错误'))
            }

        # 获取数据库配置
        db_config = Config.DATABASE_CONFIG

        # 验证备份文件格式 - 只支持.backup格式
        if not filename.endswith('.backup'):
            return {
                'success': False,
                'message': f'不支持的备份文件格式: {filename}。只支持.backup格式',
                'error': 'Unsupported format'
            }

        # 设置环境变量（PostgreSQL密码和编码）
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']
        env['PGCLIENTENCODING'] = 'UTF8'  # 设置PostgreSQL客户端编码
        env['LC_ALL'] = 'C.UTF-8'  # 设置系统编码

        # 使用 pg_restore 恢复自定义格式 - 固定使用覆盖模式
        cmd = [
            'pg_restore',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}",
            f"--username={db_config['user']}",
            f"--dbname={db_config['database']}",
            '--no-password',
            '--verbose',
            '--no-owner',  # 不恢复所有者信息
            '--no-privileges',  # 不恢复权限信息
            '--clean',  # 清理现有对象
            '--if-exists',  # 如果对象存在才清理
            '--single-transaction',  # 使用单事务
            backup_path
        ]



        # 最终检查
        if not os.path.exists(backup_path):
            return {
                'success': False,
                'message': f'备份文件不存在: {backup_path}',
                'error': 'File not found'
            }

        # 执行恢复
        try:
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                encoding='utf-8',  # 强制使用UTF-8编码
                errors='replace',  # 遇到编码错误时替换为占位符
                timeout=600,  # 10分钟超时
                cwd=os.path.dirname(backup_path)  # 设置工作目录
            )

        except subprocess.TimeoutExpired as e:
            return {
                'success': False,
                'message': '恢复超时，请检查数据库连接或备份文件大小',
                'error': 'Restore timeout'
            }
        except FileNotFoundError as e:
            return {
                'success': False,
                'message': f'pg_restore 命令未找到，请确保 PostgreSQL 客户端已安装: {str(e)}',
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'执行恢复命令失败: {str(e)}',
                'error': str(e)
            }

        # 分析执行结果
        stderr_output = result.stderr or ''
        stdout_output = result.stdout or ''

        # 简化的错误处理 - 只处理覆盖模式
        if result.returncode == 0:
            return {
                'success': True,
                'message': '数据恢复成功',
                'output': stdout_output
            }
        else:
            # 检查是否有严重错误
            serious_error_patterns = [
                'fatal',
                'connection refused',
                'authentication failed',
                'database .* does not exist',
                'permission denied',
                'could not connect'
            ]

            stderr_lower = stderr_output.lower()
            has_serious_errors = any(
                pattern in stderr_lower for pattern in serious_error_patterns
            )

            if has_serious_errors:
                error_msg = stderr_output if stderr_output else stdout_output
                return {
                    'success': False,
                    'message': f'数据恢复失败: {error_msg[:300]}...' if len(error_msg) > 300 else f'数据恢复失败: {error_msg}',
                    'error': error_msg
                }
            else:
                # 可能是一些警告或非致命错误
                return {
                    'success': True,
                    'message': '数据恢复完成（有警告）',
                    'output': stdout_output,
                    'warnings': stderr_output
                }

    except Exception as e:
        return {
            'success': False,
            'message': f'恢复过程中发生错误: {str(e)}',
            'error': str(e)
        }

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    if i == 0:
        return f"{int(size_bytes)} {size_names[i]}"
    else:
        return f"{size_bytes:.1f} {size_names[i]}"
