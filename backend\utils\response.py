from flask import jsonify

class ResponseUtils:
    """响应工具类"""
    
    @staticmethod
    def success(data=None, message="操作成功", code=0):
        """成功响应"""
        response = {
            "code": code,
            "message": message,
            "data": data,
            "timestamp": None
        }
        return jsonify(response), 200  # HTTP状态码始终为200，业务状态码为0
    
    @staticmethod
    def error(message="操作失败", code=400, data=None):
        """错误响应"""
        response = {
            "code": code,
            "message": message,
            "data": data,
            "timestamp": None
        }
        return jsonify(response), code
    
    @staticmethod
    def unauthorized(message="未授权访问"):
        """未授权响应"""
        return ResponseUtils.error(message, 401)
    
    @staticmethod
    def forbidden(message="禁止访问"):
        """禁止访问响应"""
        return ResponseUtils.error(message, 403)
    
    @staticmethod
    def not_found(message="资源不存在"):
        """资源不存在响应"""
        return ResponseUtils.error(message, 404)

    @staticmethod
    def bad_request(message="请求参数错误"):
        """请求参数错误响应"""
        return ResponseUtils.error(message, 400)
