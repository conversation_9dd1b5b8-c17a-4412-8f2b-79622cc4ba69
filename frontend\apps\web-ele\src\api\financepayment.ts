import { requestClient } from '#/api/request';

// 回款登记搜索参数接口
export interface FinancePaymentSearchParams {
  page?: number;
  pageSize?: number;
  date?: string;
  start_date?: string;
  end_date?: string;
  shop?: string;
  category?: string;
}

// 回款登记数据接口
export interface FinancePayment {
  id?: number;
  shop: string;
  record_date: string;
  category: string;
  amount: number;
  screenshot?: string;
  created_at?: string;
  updated_at?: string;
}

// 分类数据接口
export interface Category {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 店铺数据接口
export interface Store {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

/**
 * 获取回款登记列表
 */
export async function fetchFinancePaymentList(params: FinancePaymentSearchParams) {
  return requestClient.get<{
    data: FinancePayment[];
    total: number;
    page: number;
    pageSize: number;
  }>('/financepayment', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 添加回款登记记录
 */
export async function addFinancePayment(data: FinancePayment) {
  return requestClient.post<{ data: FinancePayment }>('/financepayment', data);
}

/**
 * 更新回款登记记录
 */
export async function updateFinancePayment(id: number, data: FinancePayment) {
  return requestClient.put<{ data: FinancePayment }>(`/financepayment/${id}`, data);
}

/**
 * 删除回款登记记录
 */
export async function deleteFinancePayment(id: number) {
  return requestClient.delete(`/financepayment/${id}`);
}

/**
 * 批量删除回款登记记录
 */
export async function batchDeleteFinancePayment(ids: number[]) {
  return requestClient.post('/financepayment/batch-delete', { ids });
}

/**
 * 获取线下店铺列表
 */
export async function fetchOfflineStores() {
  return requestClient.get<{
    data: Store[];
  }>('/offline-stores', {
    responseReturn: 'body',
  });
}

/**
 * 获取收入分类列表（用于回款登记的分类选择）
 */
export async function fetchIncomeCategories() {
  return requestClient.get<{
    data: Category[];
  }>('/income-categories', {
    responseReturn: 'body',
  });
}
