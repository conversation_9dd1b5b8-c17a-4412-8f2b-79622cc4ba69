#!/usr/bin/env python3
"""数据库性能监控和统计信息更新脚本
注意：索引创建已移至 init_database.py，此脚本主要用于：
1. 更新数据库统计信息 (ANALYZE)
2. 查询性能测试
3. 显示索引和表统计信息
"""

import psycopg2
import os
import sys
from datetime import datetime

# 添加父目录到路径，以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def update_database_statistics():
    """更新数据库统计信息"""
    print("=== 更新数据库统计信息 ===\n")

    conn = None
    try:
        # 连接到PostgreSQL数据库
        print("连接到PostgreSQL数据库...")
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        conn.autocommit = True
        cursor = conn.cursor()

        # 1. 更新表统计信息
        print("1. 更新表统计信息...")
        tables_to_analyze = ['finance', 'orderlist', 'paymentreceipt', 'users', 'roles', 'permissions',
                           'income_categories', 'expenditure_categories',
                           'online_stores', 'offline_stores']

        for table in tables_to_analyze:
            try:
                cursor.execute(f"ANALYZE {table}")
                print(f"  ✅ 分析表: {table}")
            except Exception as e:
                print(f"  ❌ 分析表失败 {table}: {str(e)}")

        # 2. 显示索引信息
        print("\n2. 显示现有索引信息...")
        cursor.execute("""
            SELECT
                schemaname,
                tablename,
                indexname,
                indexdef
            FROM pg_indexes
            WHERE schemaname = 'public'
                AND (tablename = 'finance' OR tablename = 'orderlist' OR tablename = 'paymentreceipt')
                AND indexname LIKE 'idx_%'
            ORDER BY tablename, indexname
        """)

        indexes_info = cursor.fetchall()
        for _, table, index_name, _ in indexes_info:
            print(f"  📊 {table}.{index_name}")

        # 3. 显示表统计信息
        print("\n3. 显示表统计信息...")
        cursor.execute("""
            SELECT
                relname as tablename,
                n_live_tup as live_tuples,
                n_dead_tup as dead_tuples,
                last_analyze
            FROM pg_stat_user_tables
            WHERE schemaname = 'public'
                AND relname IN ('finance', 'orderlist', 'paymentreceipt')
            ORDER BY relname
        """)

        stats_info = cursor.fetchall()
        for table, live_tuples, dead_tuples, last_analyze in stats_info:
            print(f"  📈 {table}: {live_tuples} 活跃记录, {dead_tuples} 死记录, 最后分析: {last_analyze}")

        print("\n✅ 统计信息更新完成！")

        print("\n=== 维护建议 ===")
        print("1. 建议每周或每月执行一次统计信息更新")
        print("2. 监控慢查询日志，关注性能变化")
        print("3. 如需创建新索引，请修改 init_database.py")
        print("4. 定期执行 VACUUM 清理死记录")
        print("5. 使用此脚本进行定期性能监控")

    except Exception as e:
        print(f"❌ 统计信息更新失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def check_query_performance():
    """检查查询性能"""
    print("\n=== 查询性能测试 ===")
    
    conn = None
    try:
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()
        
        # 测试查询 - 重点测试订单统计相关查询
        test_queries = [
            ("订单统计主查询", """
                SELECT
                    COUNT(*) as total_orders,
                    SUM(sale_price + box_fee + delivery_fee) as total_sales,
                    SUM(total_profit) as total_profit,
                    AVG(sale_price + box_fee + delivery_fee) as avg_order_value
                FROM orderlist
                WHERE isdelete = 0
                    AND date >= '2024-01-01'
                    AND date <= '2024-12-31'
                    AND shop = '美团'
            """),

            ("回款统计关联查询", """
                SELECT
                    COALESCE(SUM(amount), 0) as total_payment_amount
                FROM paymentreceipt
                WHERE is_deleted = 0
                    AND date >= '2024-01-01'
                    AND date <= '2024-12-31'
                    AND shop = '美团'
            """),

            ("订单店铺分布查询", """
                SELECT
                    shop,
                    SUM(sale_price + box_fee + delivery_fee) as total_sales,
                    COUNT(*) as order_count
                FROM orderlist
                WHERE isdelete = 0
                    AND date >= '2024-01-01'
                    AND date <= '2024-12-31'
                GROUP BY shop
                ORDER BY total_sales DESC
            """),

            ("订单每日统计查询", """
                SELECT
                    shop,
                    date,
                    SUM(sale_price + box_fee + delivery_fee) as sales_amount,
                    SUM(total_profit) as profit_amount,
                    COUNT(*) as order_count
                FROM orderlist
                WHERE isdelete = 0
                    AND date >= '2024-01-01'
                    AND date <= '2024-12-31'
                GROUP BY shop, date
                ORDER BY shop, date
            """),
        ]
        
        for query_name, query in test_queries:
            start_time = datetime.now()
            cursor.execute(query)
            results = cursor.fetchall()
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds() * 1000
            print(f"  🔍 {query_name}: {len(results)} 条结果, 耗时 {duration:.2f}ms")
            
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    update_database_statistics()
    check_query_performance()
