#!/usr/bin/env python3
"""
权限管理工具 - 用于新增页面和按钮权限
"""

import psycopg2
import psycopg2.extras
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config import Config

class PermissionManager:
    """权限管理器"""

    def __init__(self):
        self.db_config = Config.DATABASE_CONFIG

    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(**self.db_config)
        return conn
    
    def add_menu_permission(self, code, name, description, parent_code=None):
        """添加菜单权限"""
        return self._add_permission(code, name, 'menu', description, parent_code)
    
    def add_button_permission(self, code, name, description, parent_code):
        """添加按钮权限"""
        return self._add_permission(code, name, 'button', description, parent_code)
    
    def _add_permission(self, code, name, perm_type, description, parent_code=None):
        """添加权限（内部方法）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查权限码是否已存在
            cursor.execute('SELECT id FROM permissions WHERE code = %s', (code,))
            if cursor.fetchone():
                return {'success': False, 'message': f'权限码 {code} 已存在'}

            # 检查权限名称是否已存在
            cursor.execute('SELECT id FROM permissions WHERE name = %s', (name,))
            if cursor.fetchone():
                return {'success': False, 'message': f'权限名称 {name} 已存在'}

            # 如果有父权限，检查父权限是否存在
            if parent_code:
                cursor.execute('SELECT id FROM permissions WHERE code = %s', (parent_code,))
                if not cursor.fetchone():
                    return {'success': False, 'message': f'父权限 {parent_code} 不存在'}

            # 插入新权限
            cursor.execute('''
                INSERT INTO permissions (code, name, type, description, parent_code, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (code, name, perm_type, description, parent_code, datetime.now(), datetime.now()))

            permission_id = cursor.fetchone()[0]
            conn.commit()
            
            return {
                'success': True, 
                'message': f'{perm_type}权限 {name} 添加成功',
                'permission_id': permission_id
            }
            
        except Exception as e:
            conn.rollback()
            return {'success': False, 'message': f'添加权限失败: {str(e)}'}
        finally:
            conn.close()
    
    def add_module_permissions(self, module_name, module_code, parent_code=None, buttons=None):
        """批量添加模块权限（菜单+按钮）"""
        if buttons is None:
            buttons = ['add', 'edit', 'delete', 'batchDelete', 'view']
        
        results = []
        
        # 1. 添加菜单权限
        menu_code = f'MENU_{module_code.upper()}'
        menu_result = self.add_menu_permission(
            menu_code, 
            f'{module_name}管理', 
            f'{module_name}管理菜单访问权限',
            parent_code
        )
        results.append(menu_result)
        
        if not menu_result['success']:
            return results
        
        # 2. 添加按钮权限
        button_configs = {
            'add': f'新增{module_name}',
            'edit': f'编辑{module_name}',
            'delete': f'删除{module_name}',
            'batchDelete': f'批量删除{module_name}',
            'view': f'查看{module_name}',
            'export': f'导出{module_name}',
            'import': f'导入{module_name}',
            'status': f'{module_name}状态切换'
        }
        
        for button in buttons:
            if button in button_configs:
                button_code = f'{module_code.upper()}_{button.upper()}'
                button_result = self.add_button_permission(
                    button_code,
                    button_configs[button],
                    f'{button_configs[button]}按钮权限',
                    menu_code
                )
                results.append(button_result)
        
        return results
    
    def assign_permissions_to_role(self, role_id, permission_codes):
        """为角色分配权限"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        try:
            # 获取权限ID
            permission_ids = []
            for code in permission_codes:
                cursor.execute('SELECT id FROM permissions WHERE code = %s', (code,))
                result = cursor.fetchone()
                if result:
                    permission_ids.append(result['id'])

            # 分配权限
            for permission_id in permission_ids:
                cursor.execute('''
                    INSERT INTO role_permissions (role_id, permission_id, created_at)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (role_id, permission_id) DO NOTHING
                ''', (role_id, permission_id, datetime.now()))

            conn.commit()
            return {'success': True, 'message': f'成功为角色分配 {len(permission_ids)} 个权限'}

        except Exception as e:
            conn.rollback()
            return {'success': False, 'message': f'分配权限失败: {str(e)}'}
        finally:
            conn.close()
    
    def list_permissions(self, perm_type=None):
        """列出权限"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        if perm_type:
            cursor.execute('''
                SELECT id, code, name, type, description, parent_code
                FROM permissions
                WHERE type = %s
                ORDER BY code
            ''', (perm_type,))
        else:
            cursor.execute('''
                SELECT id, code, name, type, description, parent_code
                FROM permissions
                ORDER BY type, code
            ''')

        permissions = cursor.fetchall()
        conn.close()

        return [dict(perm) for perm in permissions]
    
    def delete_permission(self, code):
        """删除权限"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查是否有子权限
            cursor.execute('SELECT COUNT(*) FROM permissions WHERE parent_code = %s', (code,))
            child_count = cursor.fetchone()[0]

            if child_count > 0:
                return {'success': False, 'message': f'权限 {code} 有子权限，无法删除'}

            # 删除角色权限关联
            cursor.execute('''
                DELETE FROM role_permissions
                WHERE permission_id = (SELECT id FROM permissions WHERE code = %s)
            ''', (code,))

            # 删除权限
            cursor.execute('DELETE FROM permissions WHERE code = %s', (code,))

            if cursor.rowcount == 0:
                return {'success': False, 'message': f'权限 {code} 不存在'}

            conn.commit()
            return {'success': True, 'message': f'权限 {code} 删除成功'}
            
        except Exception as e:
            conn.rollback()
            return {'success': False, 'message': f'删除权限失败: {str(e)}'}
        finally:
            conn.close()

def main():
    """主函数 - 交互式权限管理"""
    manager = PermissionManager()
    
    print("=" * 60)
    print("权限管理工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 添加单个菜单权限")
        print("2. 添加单个按钮权限")
        print("3. 批量添加模块权限")
        print("4. 列出所有权限")
        print("5. 为角色分配权限")
        print("6. 删除权限")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            print("退出权限管理工具")
            break
        elif choice == '1':
            add_single_menu_permission(manager)
        elif choice == '2':
            add_single_button_permission(manager)
        elif choice == '3':
            add_module_permissions(manager)
        elif choice == '4':
            list_permissions(manager)
        elif choice == '5':
            assign_permissions_to_role(manager)
        elif choice == '6':
            delete_permission(manager)
        else:
            print("无效选择，请重新输入")

def add_single_menu_permission(manager):
    """添加单个菜单权限"""
    print("\n=== 添加菜单权限 ===")
    code = input("权限码 (如 MENU_PRODUCT): ").strip()
    name = input("权限名称 (如 产品管理): ").strip()
    description = input("权限描述: ").strip()
    parent_code = input("父权限码 (可选，如 MENU_SYSTEM): ").strip() or None
    
    result = manager.add_menu_permission(code, name, description, parent_code)
    print(f"结果: {result['message']}")

def add_single_button_permission(manager):
    """添加单个按钮权限"""
    print("\n=== 添加按钮权限 ===")
    code = input("权限码 (如 PRODUCT_ADD): ").strip()
    name = input("权限名称 (如 新增产品): ").strip()
    description = input("权限描述: ").strip()
    parent_code = input("父权限码 (必填，如 MENU_PRODUCT): ").strip()
    
    if not parent_code:
        print("按钮权限必须指定父权限码")
        return
    
    result = manager.add_button_permission(code, name, description, parent_code)
    print(f"结果: {result['message']}")

def add_module_permissions(manager):
    """批量添加模块权限"""
    print("\n=== 批量添加模块权限 ===")
    module_name = input("模块名称 (如 产品): ").strip()
    module_code = input("模块代码 (如 PRODUCT): ").strip()
    parent_code = input("父权限码 (可选，如 MENU_SYSTEM): ").strip() or None
    
    print("\n可选按钮权限: add, edit, delete, batchDelete, view, export, import, status")
    buttons_input = input("需要的按钮权限 (用逗号分隔，默认: add,edit,delete,batchDelete,view): ").strip()
    
    if buttons_input:
        buttons = [b.strip() for b in buttons_input.split(',')]
    else:
        buttons = ['add', 'edit', 'delete', 'batchDelete', 'view']
    
    results = manager.add_module_permissions(module_name, module_code, parent_code, buttons)
    
    print("\n添加结果:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['message']}")

def list_permissions(manager):
    """列出权限"""
    print("\n=== 权限列表 ===")
    perm_type = input("权限类型 (menu/button，留空显示全部): ").strip() or None
    
    permissions = manager.list_permissions(perm_type)
    
    if not permissions:
        print("没有找到权限")
        return
    
    print(f"\n找到 {len(permissions)} 个权限:")
    print("-" * 80)
    print(f"{'权限码':<20} {'名称':<15} {'类型':<8} {'父权限':<15} {'描述'}")
    print("-" * 80)
    
    for perm in permissions:
        print(f"{perm['code']:<20} {perm['name']:<15} {perm['type']:<8} {perm['parent_code'] or '':<15} {perm['description']}")

def assign_permissions_to_role(manager):
    """为角色分配权限"""
    print("\n=== 为角色分配权限 ===")
    role_id = input("角色ID: ").strip()
    
    if not role_id.isdigit():
        print("角色ID必须是数字")
        return
    
    permission_codes = input("权限码 (用逗号分隔): ").strip()
    codes = [code.strip() for code in permission_codes.split(',')]
    
    result = manager.assign_permissions_to_role(int(role_id), codes)
    print(f"结果: {result['message']}")

def delete_permission(manager):
    """删除权限"""
    print("\n=== 删除权限 ===")
    code = input("要删除的权限码: ").strip()
    
    confirm = input(f"确认删除权限 {code}? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消删除")
        return
    
    result = manager.delete_permission(code)
    print(f"结果: {result['message']}")

if __name__ == '__main__':
    main()
