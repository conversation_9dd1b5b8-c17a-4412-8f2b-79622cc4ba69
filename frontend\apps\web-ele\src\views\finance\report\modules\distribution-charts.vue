<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import {
  ElCard,
  ElRow,
  ElCol
} from 'element-plus';
import * as echarts from 'echarts';
import type { DistributionData } from '../../../../api/finance';

defineOptions({ name: 'DistributionCharts' });

interface Props {
  distributionData: DistributionData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 图表引用
const incomeChartRef = ref<HTMLDivElement>();
const expenseChartRef = ref<HTMLDivElement>();
const netProfitChartRef = ref<HTMLDivElement>();

// 图表实例
let incomeChart: echarts.ECharts | null = null;
let expenseChart: echarts.ECharts | null = null;
let netProfitChart: echarts.ECharts | null = null;

// 净利润分布数据（按店铺统计）
const netProfitData = computed(() => {
  if (!props.distributionData || !props.distributionData.netProfitDistribution) {
    return [];
  }

  // 绿色系（正数）
  const greenColors = ['#52C41A', '#73D13D', '#95DE64', '#B7EB8F', '#D9F7BE', '#F6FFED'];
  // 红色系（负数）
  const redColors = ['#FF4D4F', '#FF7875', '#FFA39E', '#FFCCC7', '#FFE1E1', '#FFF1F0'];

  return props.distributionData.netProfitDistribution.map((item, index) => {
    if (item.value >= 0) {
      // 正数使用绿色系
      return {
        name: item.name,
        value: item.value,
        color: greenColors[index % greenColors.length]
      };
    } else {
      // 负数使用红色系
      return {
        name: item.name,
        value: item.value,
        color: redColors[index % redColors.length]
      };
    }
  });
});

// 收入分类数据
const incomeCategoryData = computed(() => {
  if (!props.distributionData || !props.distributionData.incomeCategory) {
    return [];
  }

  const colors = ['#67C23A', '#85CE61', '#95D475', '#B3E19D', '#D4EDDA'];

  return props.distributionData.incomeCategory.map((item, index) => ({
    name: item.name,
    value: item.value,
    color: colors[index % colors.length]
  }));
});

// 支出分类数据
const expenseCategoryData = computed(() => {
  if (!props.distributionData || !props.distributionData.expenseCategory) {
    return [];
  }

  const colors = ['#F56C6C', '#F78989', '#F9A3A4', '#FBBCBC', '#FDD8D8'];

  return props.distributionData.expenseCategory.map((item, index) => ({
    name: item.name,
    value: item.value,
    color: colors[index % colors.length]
  }));
});

// 创建南丁格尔玫瑰图配置
function createRoseChartOption(data: any[], title: string) {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const value = Number(params.value).toFixed(2);
        const percent = Number(params.percent).toFixed(2);
        return `${params.seriesName} <br/>${params.name}: ¥${value} (${percent}%)`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      textStyle: {
        color: '#333',
        textBorderColor: '#fff',
        textBorderWidth: 1
      }
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: ['20%', '70%'], // 南丁格尔玫瑰图
        center: ['40%', '50%'],
        roseType: 'area', // 南丁格尔玫瑰图类型
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.map((item: any) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  };
}

// 初始化净利润分布图
function initNetProfitChart() {
  if (!netProfitChartRef.value) return;

  // 检查容器尺寸，限制重试次数
  const rect = netProfitChartRef.value.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) {
    const retryCount = (initNetProfitChart as any).retryCount || 0;
    if (retryCount < 10) {
      (initNetProfitChart as any).retryCount = retryCount + 1;
      setTimeout(() => initNetProfitChart(), 200);
      return;
    }
  }

  (initNetProfitChart as any).retryCount = 0;
  netProfitChart = echarts.init(netProfitChartRef.value);
  updateNetProfitChart();
}

// 更新净利润分布图
function updateNetProfitChart() {
  if (!netProfitChart) return;

  const option = createRoseChartOption(netProfitData.value, '净利润分布');
  netProfitChart.setOption(option);
}

// 初始化收入分类图
function initIncomeChart() {
  if (!incomeChartRef.value) return;

  // 检查容器尺寸，限制重试次数
  const rect = incomeChartRef.value.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) {
    const retryCount = (initIncomeChart as any).retryCount || 0;
    if (retryCount < 10) {
      (initIncomeChart as any).retryCount = retryCount + 1;
      setTimeout(() => initIncomeChart(), 200);
      return;
    }
  }

  (initIncomeChart as any).retryCount = 0;
  incomeChart = echarts.init(incomeChartRef.value);
  updateIncomeChart();
}

// 更新收入分类图
function updateIncomeChart() {
  if (!incomeChart) return;

  const option = createRoseChartOption(incomeCategoryData.value, '收入分类');
  incomeChart.setOption(option);
}

// 初始化支出分类图
function initExpenseChart() {
  if (!expenseChartRef.value) return;

  // 检查容器尺寸，限制重试次数
  const rect = expenseChartRef.value.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) {
    const retryCount = (initExpenseChart as any).retryCount || 0;
    if (retryCount < 10) {
      (initExpenseChart as any).retryCount = retryCount + 1;
      setTimeout(() => initExpenseChart(), 200);
      return;
    }
  }

  (initExpenseChart as any).retryCount = 0;
  expenseChart = echarts.init(expenseChartRef.value);
  updateExpenseChart();
}

// 更新支出分类图
function updateExpenseChart() {
  if (!expenseChart) return;

  const option = createRoseChartOption(expenseCategoryData.value, '支出分类');
  expenseChart.setOption(option);
}

// 初始化所有图表
function initCharts() {
  nextTick(() => {
    initIncomeChart();
    initExpenseChart();
    initNetProfitChart();
  });
}

// 更新所有图表
function updateCharts() {
  updateIncomeChart();
  updateExpenseChart();
  updateNetProfitChart();
}

// 窗口大小变化时重绘图表
function handleResize() {
  incomeChart?.resize();
  expenseChart?.resize();
  netProfitChart?.resize();
}

// 监听数据变化
watch(() => props.distributionData, () => {
  nextTick(() => {
    updateCharts();
  });
}, { deep: true });

// 组件挂载时初始化图表
onMounted(() => {
  nextTick(() => {
    initCharts();
  });
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  incomeChart?.dispose();
  expenseChart?.dispose();
  netProfitChart?.dispose();
});
</script>

<template>
  <div class="distribution-charts">
    <ElRow :gutter="16">


      <!-- 收入分类 -->
      <ElCol :xs="24" :sm="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span>收入分类</span>
            </div>
          </template>
          <div
            ref="incomeChartRef"
            class="echarts-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>

      <!-- 支出分类 -->
      <ElCol :xs="24" :sm="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span>支出分类</span>
            </div>
          </template>
          <div
            ref="expenseChartRef"
            class="echarts-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>

      <!-- 净利润分布（按店铺） -->
      <ElCol :xs="24" :sm="8">
        <ElCard class="chart-card">
          <template #header>
            <div class="card-header">
              <span>净利润分布</span>
            </div>
          </template>
          <div
            ref="netProfitChartRef"
            class="echarts-container"
            v-loading="loading"
          ></div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped>
.distribution-charts {
  width: 100%;
}

.chart-card {
  height: 300px;
}

.chart-card :deep(.el-card__body) {
  padding: 16px;
  height: calc(100% - 60px);
}

.card-header {
  font-weight: 600;
  font-size: 16px;
}

.echarts-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-card {
    height: 250px;
    margin-bottom: 16px;
  }

  .echarts-container {
    min-height: 180px;
  }
}

@media (max-width: 480px) {
  .chart-card {
    margin-bottom: 12px;
  }

  .echarts-container {
    min-height: 150px;
  }
}


</style>
