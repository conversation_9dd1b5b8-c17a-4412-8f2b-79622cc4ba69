from flask import Blueprint, request, g
from functools import wraps
from models.permission import Permission
from utils.response import ResponseUtils
from utils.jwt_utils import JWTUtils

# 创建权限管理蓝图
permission_bp = Blueprint('permission', __name__, url_prefix='/api/permission')

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return ResponseUtils.unauthorized("令牌格式错误")
        
        if not token:
            return ResponseUtils.unauthorized("缺少访问令牌")
        
        # 验证令牌
        payload = JWTUtils.verify_access_token(token)
        if not payload:
            return ResponseUtils.unauthorized("无效的访问令牌")
        
        # 将用户信息存储到g对象中
        g.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

@permission_bp.route('/all', methods=['GET'])
@token_required
def get_all_permissions():
    """获取所有权限接口（树形结构）"""
    try:
        permission_model = Permission()
        permissions = permission_model.get_all_permissions()

        return ResponseUtils.success(permissions, "获取权限列表成功")

    except Exception as e:
        print(f"获取权限列表错误: {str(e)}")
        return ResponseUtils.error("获取权限列表失败", 500)

@permission_bp.route('/user/<int:user_id>', methods=['GET'])
@token_required
def get_user_permissions(user_id):
    """获取用户权限接口"""
    try:
        permission_model = Permission()
        permissions = permission_model.get_user_permissions(user_id)

        return ResponseUtils.success(permissions, "获取用户权限成功")

    except Exception as e:
        print(f"获取用户权限错误: {str(e)}")
        return ResponseUtils.error("获取用户权限失败", 500)

@permission_bp.route('/user/<int:user_id>/codes', methods=['GET'])
@token_required
def get_user_permission_codes(user_id):
    """获取用户权限码接口"""
    try:
        permission_model = Permission()
        codes = permission_model.get_user_permission_codes(user_id)

        return ResponseUtils.success(codes, "获取用户权限码成功")

    except Exception as e:
        print(f"获取用户权限码错误: {str(e)}")
        return ResponseUtils.error("获取用户权限码失败", 500)

@permission_bp.route('/user/<int:user_id>/menus', methods=['GET'])
@token_required
def get_user_menu_permissions(user_id):
    """获取用户菜单权限接口"""
    try:
        permission_model = Permission()
        permissions = permission_model.get_menu_permissions_by_user(user_id)

        return ResponseUtils.success(permissions, "获取用户菜单权限成功")

    except Exception as e:
        print(f"获取用户菜单权限错误: {str(e)}")
        return ResponseUtils.error("获取用户菜单权限失败", 500)

@permission_bp.route('/check', methods=['POST'])
@token_required
def check_permission():
    """检查权限接口"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        permission_code = data.get('permission_code')

        if not user_id or not permission_code:
            return ResponseUtils.bad_request("用户ID和权限码不能为空")

        permission_model = Permission()
        has_permission = permission_model.check_user_permission(user_id, permission_code)

        return ResponseUtils.success({'has_permission': has_permission}, "权限检查完成")

    except Exception as e:
        print(f"权限检查错误: {str(e)}")
        return ResponseUtils.error("权限检查失败", 500)
