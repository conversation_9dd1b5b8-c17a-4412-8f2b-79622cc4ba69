#!/usr/bin/env python3
"""线下店铺管理API"""

from flask import Blueprint, request, jsonify
import sqlite3
from datetime import datetime
from .auth import token_required, check_permission
from .utils import get_db_connection, format_response, validate_required_fields
from utils.datetime_utils import format_datetime_list

offline_store_bp = Blueprint('offline_store', __name__, url_prefix='/api')

@offline_store_bp.route('/offline-stores', methods=['GET'])
@token_required
@check_permission('MENU_STORE_OFFLINE')
def get_offline_stores():
    """获取线下店铺列表"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        name = request.args.get('name', '').strip()
        status = request.args.get('status', '').strip()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_conditions = ["isdelete = 0"]
        params = []
        
        if name:
            where_conditions.append("name LIKE %s")
            params.append(f"%{name}%")
            
        if status:
            where_conditions.append("status = %s")
            params.append(int(status))
        
        where_clause = " AND ".join(where_conditions)
        
        # 获取总数
        count_sql = f"SELECT COUNT(*) FROM offline_stores WHERE {where_clause}"
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, name, status, created_at, updated_at 
            FROM offline_stores 
            WHERE {where_clause}
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """
        cursor.execute(data_sql, params + [page_size, offset])
        
        stores = []
        for row in cursor.fetchall():
            stores.append({
                'id': row[0],
                'name': row[1],
                'status': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        # 格式化时间字段
        stores = format_datetime_list(stores, ['created_at', 'updated_at'])

        conn.close()

        return format_response(
            data=stores,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        return format_response(success=False, message=f"获取线下店铺列表失败: {str(e)}")

@offline_store_bp.route('/offline-stores', methods=['POST'])
@token_required
@check_permission('STORE_OFFLINE_ADD')
def create_offline_store():
    """创建线下店铺"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error
        
        name = data['name'].strip()
        status = data.get('status', 1)
        
        # 检查名称是否已存在
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id FROM offline_stores WHERE name = %s AND isdelete = 0", (name,))
        if cursor.fetchone():
            conn.close()
            return format_response(success=False, message="线下店铺名称已存在")
        
        # 插入新记录
        now = datetime.now().isoformat()
        cursor.execute("""
            INSERT INTO offline_stores (name, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s)
            RETURNING id
        """, (name, status, now, now))

        store_id = cursor.fetchone()[0]
        conn.commit()
        conn.close()
        
        return format_response(
            data={'id': store_id, 'name': name, 'status': status},
            message="线下店铺创建成功"
        )
        
    except Exception as e:
        return format_response(success=False, message=f"创建线下店铺失败: {str(e)}")

@offline_store_bp.route('/offline-stores/<int:store_id>', methods=['PUT'])
@token_required
@check_permission('STORE_OFFLINE_EDIT')
def update_offline_store(store_id):
    """更新线下店铺"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error
        
        name = data['name'].strip()
        status = data.get('status', 1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM offline_stores WHERE id = %s AND isdelete = 0", (store_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="线下店铺不存在")
        
        # 检查名称是否已被其他记录使用
        cursor.execute("SELECT id FROM offline_stores WHERE name = %s AND id != %s AND isdelete = 0", (name, store_id))
        if cursor.fetchone():
            conn.close()
            return format_response(success=False, message="线下店铺名称已存在")
        
        # 更新记录
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE offline_stores 
            SET name = %s, status = %s, updated_at = %s
            WHERE id = %s
        """, (name, status, now, store_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="线下店铺更新成功")
        
    except Exception as e:
        return format_response(success=False, message=f"更新线下店铺失败: {str(e)}")

@offline_store_bp.route('/offline-stores/<int:store_id>', methods=['DELETE'])
@token_required
@check_permission('STORE_OFFLINE_DELETE')
def delete_offline_store(store_id):
    """删除线下店铺"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM offline_stores WHERE id = %s AND isdelete = 0", (store_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="线下店铺不存在")
        
        # 软删除
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE offline_stores 
            SET isdelete = 1, updated_at = %s
            WHERE id = %s
        """, (now, store_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="线下店铺删除成功")
        
    except Exception as e:
        return format_response(success=False, message=f"删除线下店铺失败: {str(e)}")

@offline_store_bp.route('/offline-stores/batch-delete', methods=['POST'])
@token_required
@check_permission('STORE_OFFLINE_BATCH_DELETE')
def batch_delete_offline_stores():
    """批量删除线下店铺"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return format_response(success=False, message="请选择要删除的线下店铺")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 软删除
        now = datetime.now().isoformat()
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f"""
            UPDATE offline_stores 
            SET isdelete = 1, updated_at = %s
            WHERE id IN ({placeholders}) AND isdelete = 0
        """, [now] + ids)
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return format_response(message=f"成功删除 {affected_rows} 个线下店铺")
        
    except Exception as e:
        return format_response(success=False, message=f"批量删除线下店铺失败: {str(e)}")

@offline_store_bp.route('/offline-stores/<int:store_id>/status', methods=['PUT'])
@token_required
@check_permission('STORE_OFFLINE_STATUS')
def update_offline_store_status(store_id):
    """更新线下店铺状态"""
    try:
        data = request.get_json()
        status = data.get('status')
        
        if status is None:
            return format_response(success=False, message="状态参数不能为空")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM offline_stores WHERE id = %s AND isdelete = 0", (store_id,))
        if not cursor.fetchone():
            conn.close()
            return format_response(success=False, message="线下店铺不存在")
        
        # 更新状态
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE offline_stores 
            SET status = %s, updated_at = %s
            WHERE id = %s
        """, (int(status), now, store_id))
        
        conn.commit()
        conn.close()
        
        return format_response(message="线下店铺状态更新成功")
        
    except Exception as e:
        return format_response(success=False, message=f"更新线下店铺状态失败: {str(e)}")
