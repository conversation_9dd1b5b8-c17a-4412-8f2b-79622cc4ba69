/**
 * 分类颜色统一管理工具
 * 确保相同分类在不同页面显示相同颜色
 */

// Element Plus Tag 支持的颜色类型
export type TagType = 'primary' | 'success' | 'info' | 'warning' | 'danger';

// 自定义颜色类型（支持十六进制颜色）
export type CustomColor = TagType | string;

// 颜色配置接口
export interface ColorConfig {
  background: string;  // 半透明背景色
  color: string;       // 实心文字颜色
}

// 收入类型的颜色组（积极色调为主，半透明背景+实心文字）
const INCOME_COLORS: ColorConfig[] = [
  { background: '#4CAF5020', color: '#4CAF50' },    // 深绿色
  { background: '#2196F320', color: '#2196F3' },    // 蓝色
  { background: '#FFC10720', color: '#FFC107' },    // 黄色
  { background: '#8BC34A20', color: '#8BC34A' },    // 浅绿色
  { background: '#03A9F420', color: '#03A9F4' },    // 亮蓝色
  { background: '#FF980020', color: '#FF9800' },    // 橙色
  { background: '#9C27B020', color: '#9C27B0' },    // 紫色
  { background: '#00BCD420', color: '#00BCD4' },    // 青色
];

// 支出类型的颜色组（较强烈的色调为主，半透明背景+实心文字）
const EXPENSE_COLORS: ColorConfig[] = [
  { background: '#F4433620', color: '#F44336' },    // 红色
  { background: '#E91E6320', color: '#E91E63' },    // 粉红色
  { background: '#9E9E9E20', color: '#9E9E9E' },    // 灰色
  { background: '#607D8B20', color: '#607D8B' },    // 蓝灰色
  { background: '#F57C0020', color: '#F57C00' },    // 深橙色
  { background: '#79554820', color: '#795548' },    // 棕色
  { background: '#CDDC3920', color: '#CDDC39' },    // 酸橙色
  { background: '#FF572220', color: '#FF5722' },    // 深橙红色
];


/**
 * 根据字符串生成一致的哈希值
 * @param str 输入字符串
 * @returns 哈希值
 */
function getStringHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash);
}

/**
 * 获取分类的统一颜色配置
 * @param category 分类名称
 * @param type 类型：'收入' | '支出' | 'income'（回款登记默认为收入性质）
 * @returns 颜色配置对象，包含背景色和文字颜色
 */
export function getCategoryColor(
  category: string,
  type: '收入' | '支出' | 'income' = 'income'
): ColorConfig {
  if (!category || category === '-') {
    return { background: '#909399', color: '#909399' }; // 灰色
  }

  // 根据类型选择颜色组
  const colorGroup = (type === '收入' || type === 'income') ? INCOME_COLORS : EXPENSE_COLORS;

  // 基于分类名称生成一致的颜色索引
  const hash = getStringHash(category);
  const colorIndex = hash % colorGroup.length;

  return colorGroup[colorIndex] || { background: '#909399', color: '#909399' };
}

/**
 * 预定义的特殊分类颜色映射（可选）
 * 如果需要为特定分类指定固定颜色，可以在这里配置
 */
const SPECIAL_CATEGORY_COLORS: Record<string, ColorConfig> = {
  // 示例：
  // '外卖': { background: '#67c23a20', color: '#67c23a' },
  //'小程序': { background: '#409eff20', color: '#409eff' },
  //'进货': { background: '#ff6b6b20', color: '#ff6b6b' },
  //'推广费': { background: '#4ecdc420', color: '#4ecdc4' },
};

/**
 * 获取分类颜色（支持特殊映射）
 * @param category 分类名称
 * @param type 类型
 * @returns 颜色配置对象
 */
export function getCategoryColorWithSpecial(
  category: string,
  type: '收入' | '支出' | 'income' = 'income'
): ColorConfig {
  // 优先检查特殊映射
  const specialColor = SPECIAL_CATEGORY_COLORS[category];
  if (specialColor) {
    return specialColor;
  }

  // 使用通用规则
  return getCategoryColor(category, type);
}
