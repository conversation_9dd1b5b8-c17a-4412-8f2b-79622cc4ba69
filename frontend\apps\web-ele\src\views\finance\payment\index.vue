<script setup lang="tsx">
import { ElButton, ElPopconfirm, ElMessage, ElCard, ElTable, ElTableColumn, ElPagination, ElImage, ElTag} from 'element-plus';
import FinanceSearch from './modules/finance-search.vue';
import FinanceOperateModal from './modules/finance-operate-modal.vue';
import { ref, computed, onMounted } from 'vue';
import TableHeaderOperation from '../../../components/table-header-operation.vue';
import { fetchFinancePaymentList, deleteFinancePayment, batchDeleteFinancePayment } from '../../../api/financepayment';
import type { FinancePaymentSearchParams } from '../../../api/financepayment';
import { getImageUrl } from '../../../utils/config';
import { useAccess } from '@vben/access';
import { useUserStore } from '@vben/stores';


import { toBeijingTime } from '../../../utils/common';
import { getCategoryColorWithSpecial } from '../../../utils/categoryColor';



// 权限控制
const { hasAccessByCodes } = useAccess();

// 用户信息
const userStore = useUserStore();

// 检查回款登记权限
function hasFinancePaymentPermission(action: string): boolean {
  const permissionMap: Record<string, string> = {
    'add': 'FINANCEPAYMENT_ADD',
    'edit': 'FINANCEPAYMENT_EDIT',
    'delete': 'FINANCEPAYMENT_DELETE',
    'batchDelete': 'FINANCEPAYMENT_BATCH_DELETE'
  };

  const permissionCode = permissionMap[action];
  return permissionCode ? hasAccessByCodes([permissionCode]) : false;
}

// 表格列配置
const columns = [
  { type: 'selection', width: 48, align: 'center', headerAlign: 'center' },
  { prop: 'shop', label: '店铺', minWidth: 120, align: 'center', headerAlign: 'center' },
  { prop: 'record_date', label: '记账日期', minWidth: 120, align: 'center', headerAlign: 'center' },
  { prop: 'category', label: '分类', minWidth: 120, align: 'center', headerAlign: 'center', formatter: (row: any) => {
    const category = row.category || '';
    if (!category || category === '-') return <span>-</span>;
    // 使用统一的分类颜色策略：回款登记属于收入性质
    const colorConfig = getCategoryColorWithSpecial(category, 'income');
    return (
      <ElTag
        class="custom-category-tag"
        style={{
          '--custom-bg-color': colorConfig.background,
          '--custom-text-color': colorConfig.color,
          '--custom-border': `1px solid ${colorConfig.color}30`,
          backgroundColor: colorConfig.background,
          color: colorConfig.color,
          border: `1px solid ${colorConfig.color}30`,
          borderRadius: '4px',
          padding: '0 8px',
          fontSize: '12px',
          lineHeight: '20px',
          height: '22px'
        }}
      >
        {category}
      </ElTag>
    );
  } },
  { prop: 'amount', label: '金额', minWidth: 100, align: 'center', headerAlign: 'center', formatter: (row: any) => {
    const amount = row.amount || 0;
    return <span style={{ color: '#67c23a', fontWeight: '600' }}>¥{amount.toFixed(2)}</span>;
  } },
  { prop: 'created_at', label: '创建时间', minWidth: 150, align: 'center', headerAlign: 'center' },
  { prop: 'updated_at', label: '更新时间', minWidth: 150, align: 'center', headerAlign: 'center' },
  { prop: 'screenshot', label: '凭证图片', minWidth: 80, align: 'center', headerAlign: 'center', formatter: (row: any) => {
    if (!row.screenshot) return <span>-</span>;

    // 使用统一的图片URL构造函数
    const imageUrl = getImageUrl(row.screenshot);

    return (
      <ElImage
        src={imageUrl}
        style="width:30px;height:30px;object-fit:cover;cursor:pointer"
        preview-src-list={[imageUrl]}
        preview-teleported
      />
    );
  } },
  {
    prop: 'operate',
    label: '操作',
    width: 150,
    align: 'center',
    headerAlign: 'center',
    fixed: 'right',
    formatter: (row: any) => (
      <div class="flex-center">
        {hasFinancePaymentPermission('edit') && (
          <ElButton type="primary" size="small" onClick={() => edit(row.id)}>
            编辑
          </ElButton>
        )}
        {hasFinancePaymentPermission('delete') && (
          <ElPopconfirm title="确认删除？" onConfirm={() => handleDelete(row.id)}>
            {{
              reference: () => (
                <ElButton type="danger" size="small">
                  删除
                </ElButton>
              )
            }}
          </ElPopconfirm>
        )}
      </div>
    )
  }
];

// 列设置用的columnChecks
const columnChecks = ref([
  { prop: 'shop', label: '店铺', checked: true },
  { prop: 'record_date', label: '记账日期', checked: true },
  { prop: 'category', label: '分类', checked: true },
  { prop: 'amount', label: '金额', checked: true },
  { prop: 'created_at', label: '创建时间', checked: true },
  { prop: 'updated_at', label: '更新时间', checked: true },
  { prop: 'screenshot', label: '凭证图片', checked: true },
  { prop: 'operate', label: '操作', checked: true }
]);

// 表格数据和分页
const rawData = ref<any[]>([]); // 当前页数据
const data = ref<any[]>([]);    // 当前页数据（可直接用rawData）
const loading = ref(false);
const checkedRowKeys = ref<number[]>([]);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);           // 总条数

// 计算当前显示的表格列
const showColumns = computed(() => {
  return columns.filter(col => {
    if (!col.prop) return true;
    const check = columnChecks.value.find(item => item.prop === col.prop);
    return check ? check.checked : true;
  });
});

// 搜索参数
interface LocalFinancePaymentSearchParams {
  dateRange: string[];
  shop: string;
  category: string;
}
const searchParams = ref<LocalFinancePaymentSearchParams>({ dateRange: [], shop: '', category: '' });

// 回款登记数据处理函数
function formatFinancePaymentData(financePayments: any[]) {
  return financePayments.map(financePayment => ({
    ...financePayment,
    shop: financePayment.shop || '-',
    record_date: financePayment.record_date || '-',
    category: financePayment.category || '-',
    amount: financePayment.amount || 0,
    // 使用统一的时间处理函数
    created_at: financePayment.created_at ? toBeijingTime(financePayment.created_at) : '-',
    updated_at: financePayment.updated_at ? toBeijingTime(financePayment.updated_at) : '-',
    screenshot: financePayment.screenshot || ''
  }));
}

// 获取回款登记数据（分页+搜索）
async function loadFinancePaymentList() {
  loading.value = true;
  try {
    // 安全地处理日期范围
    const dateRange = searchParams.value.dateRange;
    let dateParams: { date?: string; start_date?: string; end_date?: string } = {};

    if (dateRange && Array.isArray(dateRange) && dateRange.length > 0) {
      if (dateRange.length === 2 && dateRange[0] && dateRange[1]) {
        // 日期范围查询
        dateParams.start_date = dateRange[0];
        dateParams.end_date = dateRange[1];
      } else if (dateRange.length === 1 && dateRange[0]) {
        // 单个日期查询
        dateParams.date = dateRange[0];
      }
    }

    const params: FinancePaymentSearchParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...dateParams,
      shop: searchParams.value.shop || undefined,
      category: searchParams.value.category || undefined
    };

    const response = await fetchFinancePaymentList(params);

    // 检查响应格式 - 使用responseReturn: 'body'获取完整响应
    if (response && response.data) {
      const formattedData = formatFinancePaymentData(response.data);
      rawData.value = formattedData;
      data.value = formattedData;
      total.value = response.total || 0;
    } else {
      rawData.value = [];
      data.value = [];
      total.value = 0;
    }


  } catch (error) {
    console.error('获取回款登记列表失败:', error);
    ElMessage.error('获取回款登记列表失败');
    rawData.value = [];
    data.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 图片预览由ElImage组件自动处理

// 分页、搜索事件
function handlePageChange(page: number) {
  currentPage.value = page;
  loadFinancePaymentList();
}
function handleSizeChange(size: number) {
  pageSize.value = size;
  currentPage.value = 1;
  loadFinancePaymentList();
}
function getDataByPage() {
  currentPage.value = 1;
  loadFinancePaymentList();
}
function resetSearchParams() {
  searchParams.value = { dateRange: [], shop: '', category: '' };
  currentPage.value = 1;
  loadFinancePaymentList();
}

// 页面加载时获取第一页
onMounted(() => {
  loadFinancePaymentList();
});

// 操作相关逻辑
const modalVisible = ref(false);
const operateType = ref<'add' | 'edit'>('add');
const editingData = ref<any>(null);

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  modalVisible.value = true;
}

function edit(id: number) {
  operateType.value = 'edit';
  // 找到对应的数据项
  const item = data.value.find(item => item.id === id);
  if (item) {
    editingData.value = { ...item };
  } else {
    editingData.value = null;
  }
  modalVisible.value = true;
}

// 单条删除
async function handleDelete(id: number) {
  try {
    await deleteFinancePayment(id);
    ElMessage.success('删除成功');
    loadFinancePaymentList(); // 重新加载列表
  } catch (error: any) {
    console.error('删除回款登记记录失败:', error);

    // 提取错误信息
    const errorResponse = error?.response?.data;
    const errorMessage = errorResponse?.message || error?.message || '删除失败';

    // 只显示一次错误提示
    ElMessage.error(errorMessage);
  }
}

// 批量删除
async function handleBatchDelete() {
  const idsToDelete = checkedRowKeys.value.map((row: any) => typeof row === 'object' ? row.id : row);

  if (!idsToDelete.length) {
    ElMessage.warning('请选择要删除的回款登记记录');
    return;
  }

  try {
    await batchDeleteFinancePayment(idsToDelete);

    // 清空选择并重新加载列表
    checkedRowKeys.value = [];
    loadFinancePaymentList();

    ElMessage.success('批量删除成功');
  } catch (error: any) {
    console.error('批量删除回款登记记录失败:', error);
    const errorMessage = error?.response?.data?.message || error?.message || '批量删除失败';
    ElMessage.error(errorMessage);
  }
}

// 刷新功能
function handleRefresh() {
  loadFinancePaymentList();
  checkedRowKeys.value = [];
}

const isSearchCollapse = ref(true); // true为折叠，false为展开，初始为true
function handleSearchCollapseChange(val: boolean) {
  isSearchCollapse.value = val;
}

// 模态框提交后的回调
function handleModalSubmitted() {
  modalVisible.value = false;
  loadFinancePaymentList();
}

defineExpose({
  data,
  showColumns,
  currentPage,
  pageSize,
  handlePageChange,
  handleSizeChange
});
</script>

<template>
  <div class="page-container">
    <div class="search-container">
      <FinanceSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" @collapse-change="handleSearchCollapseChange" />
    </div>
    <div class="table-container" :class="{ 'search-collapsed': isSearchCollapse }">
      <ElCard class="table-card">
        <template #header>
          <div class="flex items-center justify-between">
            <p>回款记录</p>
            <TableHeaderOperation
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              :show-add="hasFinancePaymentPermission('add')"
              :show-batch-delete="hasFinancePaymentPermission('batchDelete')"
              delete-confirm-title="确认批量删除选中的回款记录吗？"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="loadFinancePaymentList"
            />
          </div>
        </template>
        <div class="table-content">
          <ElTable
            v-loading="loading"
            height="100%"
            border
            class="sm:h-full"
            :data="data"
            row-key="id"
            @selection-change="checkedRowKeys = $event"
          >
            <ElTableColumn v-for="col in showColumns" :key="col.prop" v-bind="col" />
            <template #empty>
              <div class="empty-state">
                <div class="empty-icon">📋</div>
                <div class="empty-text">暂无回款登记数据</div>
                <div class="empty-description">点击上方"新增"按钮添加第一条回款登记记录</div>
              </div>
            </template>
          </ElTable>
        </div>
        <div class="pagination-container">
          <ElPagination
            :total="total"
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 15, 20, 25, 30]"
            layout="total,prev,pager,next,sizes"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </ElCard>
    </div>
    <FinanceOperateModal
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="handleModalSubmitted"
    />

  </div>
</template>
<style lang="scss" scoped>
.page-container {
  padding: 10px;
  height: calc(100vh - 90px); /* 减去tab栏和header的高度 */
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
}

.search-container {
  flex-shrink: 0;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease-in-out;
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

.table-content {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.pagination-container {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  background: var(--el-bg-color);
}

/* 搜索框折叠时的样式调整 */
.search-collapsed .table-card {
  /* 当搜索框折叠时，表格区域可以获得更多空间 */
  height: calc(100% + 20px);
}

/* 表格行高度根据搜索框状态调整 */
.table-container :deep(.el-table .el-table__body tr) {
  height: 45px; /* 搜索框展开时的行高 */

  td {
    padding-top: 2px;
    padding-bottom: 2px;
  }
}

.search-collapsed :deep(.el-table .el-table__body tr) {
  height: 49px; /* 搜索框折叠时的行高 */

  td {
    padding-top: 2px;
    padding-bottom: 2px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: hsl(var(--accent));
        color: hsl(var(--foreground));
        font-weight: 600;
        border-bottom: 2px solid hsl(var(--border));
        font-size: 14px;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        transition: all 0.2s ease;

        &:hover {
          background-color: hsl(var(--accent-hover));
          transform: translateY(-1px);
          box-shadow: 0 2px 8px hsl(var(--overlay-content));
        }

        &.el-table__row--selected {
          background-color: hsl(var(--accent));
        }
      }

      td {
        border-bottom: 1px solid hsl(var(--border));
        padding: 12px 0;
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: hsl(var(--foreground));
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-description {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 操作按钮样式 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

:deep(.el-button) {
  &.el-button--primary {
    &.el-button--small {
      padding: 5px 12px;
      font-size: 12px;
      border-radius: 4px;
    }
  }

  &.el-button--danger {
    &.el-button--small {
      padding: 5px 12px;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}



/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 5px;
    gap: 5px;
  }

  .table-card :deep(.el-card__body) {
    padding: 8px;
  }
}

/* 自定义分类标签样式 - 覆盖 Element Plus 默认样式 */
:deep(.custom-category-tag) {
  background-color: var(--custom-bg-color) !important;
  color: var(--custom-text-color) !important;
  border: var(--custom-border) !important;
}

:deep(.custom-category-tag .el-tag__content) {
  color: inherit !important;
}
</style>

