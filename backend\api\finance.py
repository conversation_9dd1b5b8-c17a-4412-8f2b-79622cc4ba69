from flask import Blueprint, request, jsonify
from .utils import get_db_connection, format_response, validate_required_fields
from .auth import token_required, check_permission
from utils.datetime_utils import format_datetime_list


finance_bp = Blueprint('finance', __name__, url_prefix='/api')

@finance_bp.route('/finance', methods=['GET'])
@token_required
@check_permission('MENU_FINANCE')
def get_finance_list():
    """获取财务记录列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        date = request.args.get('date')
        shop = request.args.get('shop')
        type_param = request.args.get('type')
        category = request.args.get('category')
        is_reimbursed = request.args.get('is_reimbursed')
        reimbursed_status = request.args.get('reimbursed_status')
        
        # 构建查询条件
        where_conditions = ['is_deleted = 0']
        params = []
        
        # 处理日期查询 - 支持单个日期或日期范围
        if start_date and end_date:
            # 日期范围查询
            where_conditions.append('record_date >= %s AND record_date <= %s')
            params.extend([start_date, end_date])
        elif date:
            # 单个日期查询（兼容原有逻辑）
            where_conditions.append('record_date LIKE %s')
            params.append(f'%{date}%')
        
        if shop:
            where_conditions.append('shop LIKE %s')
            params.append(f'%{shop}%')
            
        if type_param:
            where_conditions.append('type = %s')
            params.append(type_param)
            
        if category:
            where_conditions.append('category LIKE %s')
            params.append(f'%{category}%')

        if is_reimbursed is not None:
            where_conditions.append('is_reimbursed = %s')
            params.append(int(is_reimbursed))

        if reimbursed_status is not None:
            where_conditions.append('reimbursed_status = %s')
            params.append(int(reimbursed_status))

        where_clause = ' AND '.join(where_conditions)
        
        # 查询总数
        count_sql = f'SELECT COUNT(*) FROM finance WHERE {where_clause}'
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]
        
        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT id, shop, record_date, type, category, amount, description, 
                   created_at, updated_at, screenshot, is_reimbursed, reimbursed_status, reimbursed_at
            FROM finance 
            WHERE {where_clause}
            ORDER BY id DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        
        finances = []
        for row in cursor.fetchall():
            finances.append({
                'id': row[0],
                'shop': row[1],
                'record_date': row[2],
                'type': row[3],
                'category': row[4],
                'amount': row[5],
                'description': row[6],
                'created_at': row[7],
                'updated_at': row[8],
                'screenshot': row[9],
                'is_reimbursed': row[10],
                'reimbursed_status': row[11],
                'reimbursed_at': row[12]
            })

        # 格式化时间字段
        finances = format_datetime_list(finances, ['created_at', 'updated_at', 'reimbursed_at'])

        return format_response(
            success=True,
            data=finances,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        print(f"获取财务记录列表错误: {str(e)}")
        return format_response(success=False, message="获取财务记录列表失败")
    finally:
        conn.close()

@finance_bp.route('/finance', methods=['POST'])
@token_required
@check_permission('FINANCE_ADD')
def create_finance():
    """创建财务记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'type', 'category', 'amount', 'description']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 处理报销逻辑
        is_reimbursed = 0
        reimbursed_status = 0
        if data.get('type') == '支出' and data.get('needReimbursement'):
            is_reimbursed = 1
            reimbursed_status = 0  # 默认未报销

        # 插入财务记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            INSERT INTO finance (shop, record_date, type, category, amount, description,
                               screenshot, is_reimbursed, reimbursed_status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        ''', (
            data['shop'],
            data['record_date'],
            data['type'],
            data['category'],
            data.get('amount', 0),
            data['description'],
            screenshot_path,
            is_reimbursed,
            reimbursed_status,
            now,
            now
        ))

        finance_id = cursor.fetchone()[0]
        conn.commit()

        return format_response(
            success=True,
            data={'id': finance_id, **data},
            message="财务记录创建成功"
        )

    except Exception as e:
        print(f"创建财务记录错误: {str(e)}")
        return format_response(success=False, message="创建财务记录失败")
    finally:
        conn.close()

@finance_bp.route('/finance/<int:finance_id>', methods=['PUT'])
@token_required
@check_permission('FINANCE_EDIT')
def update_finance(finance_id):
    """更新财务记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'type', 'category', 'amount', 'description']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查财务记录是否存在
        cursor.execute('SELECT COUNT(*) FROM finance WHERE id = %s AND is_deleted = 0', (finance_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="财务记录不存在")

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 处理报销逻辑
        is_reimbursed = 0
        reimbursed_status = 0
        if data.get('type') == '支出' and data.get('needReimbursement'):
            is_reimbursed = 1
            # 保持原有的报销状态，不重置
            cursor.execute('SELECT reimbursed_status FROM finance WHERE id = %s', (finance_id,))
            existing_status = cursor.fetchone()
            if existing_status:
                reimbursed_status = existing_status[0]

        # 更新财务记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            UPDATE finance
            SET shop = %s, record_date = %s, type = %s, category = %s, amount = %s,
                description = %s, screenshot = %s, is_reimbursed = %s, reimbursed_status = %s,
                updated_at = %s
            WHERE id = %s
        ''', (
            data['shop'],
            data['record_date'],
            data['type'],
            data['category'],
            data.get('amount', 0),
            data['description'],
            screenshot_path,
            is_reimbursed,
            reimbursed_status,
            now,
            finance_id
        ))

        conn.commit()

        return format_response(
            success=True,
            data={'id': finance_id, **data},
            message="财务记录更新成功"
        )

    except Exception as e:
        print(f"更新财务记录错误: {str(e)}")
        return format_response(success=False, message="更新财务记录失败")
    finally:
        conn.close()

@finance_bp.route('/finance/<int:finance_id>', methods=['DELETE'])
@token_required
@check_permission('FINANCE_DELETE')
def delete_finance(finance_id):
    """删除财务记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查财务记录是否存在
        cursor.execute('SELECT COUNT(*) FROM finance WHERE id = %s AND is_deleted = 0', (finance_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="财务记录不存在")

        # 软删除财务记录
        cursor.execute('UPDATE finance SET is_deleted = 1 WHERE id = %s', (finance_id,))
        conn.commit()

        return format_response(success=True, message="财务记录删除成功")

    except Exception as e:
        print(f"删除财务记录错误: {str(e)}")
        return format_response(success=False, message="删除财务记录失败")
    finally:
        conn.close()

@finance_bp.route('/finance/batch-delete', methods=['POST'])
@token_required
@check_permission('FINANCE_BATCH_DELETE')
def batch_delete_finance():
    """批量删除财务记录"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要删除的财务记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量软删除
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'UPDATE finance SET is_deleted = 1 WHERE id IN ({placeholders})', ids)
        conn.commit()

        return format_response(success=True, message="批量删除成功")

    except Exception as e:
        print(f"批量删除财务记录错误: {str(e)}")
        return format_response(success=False, message="批量删除失败")
    finally:
        conn.close()


# 报销管理相关接口
@finance_bp.route('/finance/reimburse/<int:finance_id>', methods=['PUT'])
@token_required
@check_permission('REIMBURSE_AUDIT')
def audit_reimburse(finance_id):
    """审核报销"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查财务记录是否存在且需要报销
        cursor.execute('SELECT COUNT(*) FROM finance WHERE id = %s AND is_deleted = 0 AND is_reimbursed = 1', (finance_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="财务记录不存在或不需要报销")

        # 更新报销状态为已报销，设置报销时间（不更新updated_at）
        cursor.execute('''
            UPDATE finance
            SET reimbursed_status = 1, reimbursed_at = CURRENT_TIMESTAMP
            WHERE id = %s
        ''', (finance_id,))
        conn.commit()

        return format_response(success=True, message="审核成功")

    except Exception as e:
        print(f"审核报销错误: {str(e)}")
        return format_response(success=False, message="审核失败")
    finally:
        conn.close()


@finance_bp.route('/finance/reimburse/<int:finance_id>/unaudit', methods=['PUT'])
@token_required
@check_permission('REIMBURSE_UNAUDIT')
def unaudit_reimburse(finance_id):
    """反审核报销"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查财务记录是否存在且需要报销
        cursor.execute('SELECT COUNT(*) FROM finance WHERE id = %s AND is_deleted = 0 AND is_reimbursed = 1', (finance_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="财务记录不存在或不需要报销")

        # 更新报销状态为未报销，清空报销时间（不更新updated_at）
        cursor.execute('''
            UPDATE finance
            SET reimbursed_status = 0, reimbursed_at = NULL
            WHERE id = %s
        ''', (finance_id,))
        conn.commit()

        return format_response(success=True, message="反审核成功")

    except Exception as e:
        print(f"反审核报销错误: {str(e)}")
        return format_response(success=False, message="反审核失败")
    finally:
        conn.close()


@finance_bp.route('/finance/reimburse/batch-audit', methods=['POST'])
@token_required
@check_permission('REIMBURSE_BATCH_AUDIT')
def batch_audit_reimburse():
    """批量审核报销"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要审核的财务记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量更新报销状态为已报销，设置报销时间（不更新updated_at）
        from datetime import datetime
        now = datetime.now().isoformat()
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'''
            UPDATE finance
            SET reimbursed_status = 1, reimbursed_at = %s
            WHERE id IN ({placeholders}) AND is_deleted = 0 AND is_reimbursed = 1
        ''', [now] + ids)

        affected_rows = cursor.rowcount
        conn.commit()

        return format_response(success=True, message=f"批量审核成功，共处理{affected_rows}条记录")

    except Exception as e:
        print(f"批量审核报销错误: {str(e)}")
        return format_response(success=False, message="批量审核失败")
    finally:
        conn.close()


@finance_bp.route('/finance/reimburse/batch-unaudit', methods=['POST'])
@token_required
@check_permission('REIMBURSE_BATCH_UNAUDIT')
def batch_unaudit_reimburse():
    """批量反审核报销"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要反审核的财务记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量更新报销状态为未报销，清空报销时间（不更新updated_at）
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'''
            UPDATE finance
            SET reimbursed_status = 0, reimbursed_at = NULL
            WHERE id IN ({placeholders}) AND is_deleted = 0 AND is_reimbursed = 1
        ''', ids)

        affected_rows = cursor.rowcount
        conn.commit()

        return format_response(success=True, message=f"批量反审核成功，共处理{affected_rows}条记录")

    except Exception as e:
        print(f"批量反审核报销错误: {str(e)}")
        return format_response(success=False, message="批量反审核失败")
    finally:
        conn.close()




