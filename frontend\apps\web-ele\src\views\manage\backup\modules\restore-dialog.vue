<template>
  <ElDialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="恢复数据"
    width="500px"
    :close-on-click-modal="false"
  >
    <ElAlert
      title="警告"
      type="warning"
      description="数据恢复操作将覆盖现有数据，请谨慎操作！建议在恢复前先创建当前数据的备份。重复数据将被跳过。"
      show-icon
      :closable="false"
      style="margin-bottom: 20px;"
    />

    <ElForm :model="restoreForm" label-width="100px">
      <ElFormItem label="选择备份" required>
        <ElSelect
          v-model="restoreForm.filename"
          placeholder="请选择要恢复的备份"
          style="width: 100%"
          @change="handleBackupChange"
        >
          <ElOption
            v-for="backup in filteredBackupRecords"
            :key="backup.filename"
            :value="backup.filename"
          >
            <div class="backup-option">
              <div class="backup-name">{{ backup.backup_name }}</div>
              <div class="backup-meta">
                <span class="backup-type">{{ backup.backup_type === 'full' ? '完整备份' : '部分备份' }}</span>
                <span class="backup-size">{{ backup.size }}</span>
                <span class="backup-time">{{ formatTime(backup.created_at) }}</span>
              </div>
              <div v-if="backup.backup_type === 'partial' && backup.tables.length > 0" class="backup-tables">
                包含表: {{ backup.tables.join(', ') }}
              </div>
            </div>
          </ElOption>
        </ElSelect>
      </ElFormItem>

      <ElFormItem v-if="selectedBackup" label="备份信息">
        <div class="backup-info">
          <div class="info-item">
            <span class="info-label">备份类型:</span>
            <span class="info-value">{{ selectedBackup.backup_type === 'full' ? '完整备份' : '部分备份' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">文件大小:</span>
            <span class="info-value">{{ selectedBackup.size }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间:</span>
            <span class="info-value">{{ formatTime(selectedBackup.created_at) }}</span>
          </div>
          <div v-if="selectedBackup.backup_type === 'partial'" class="info-item">
            <span class="info-label">包含表:</span>
            <span class="info-value">{{ selectedBackup.tables.join(', ') }}</span>
          </div>
        </div>
      </ElFormItem>


    </ElForm>

    <!-- 进度显示 -->
    <div v-if="loading" class="progress-container">
      <ElProgress :percentage="restoreProgress" :stroke-width="8" status="exception" />
      <p class="progress-text">正在恢复数据，请稍候...</p>
    </div>

    <template #footer>
      <ElSpace>
        <ElButton @click="handleCancel" :disabled="loading">取消</ElButton>
        <ElButton type="danger" @click="handleConfirm" :loading="loading">
          开始恢复
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElSpace,
  ElAlert,
  ElProgress
} from 'element-plus';
import { type BackupRecord } from '#/api/backup';

interface RestoreForm {
  filename: string;
}

interface Props {
  visible: boolean;
  loading?: boolean;
  restoreProgress?: number;
  backupRecords: BackupRecord[];
  initialForm?: Partial<RestoreForm>;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  restoreProgress: 0,
  initialForm: () => ({})
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  confirm: [form: RestoreForm];
  cancel: [];
}>();

// 表单数据
const restoreForm = ref<RestoreForm>({
  filename: '',
  ...props.initialForm
});

// 过滤后的备份记录 - 只显示.backup文件
const filteredBackupRecords = computed(() => {
  return props.backupRecords.filter(backup => backup.filename.endsWith('.backup'));
});

// 选中的备份信息
const selectedBackup = computed(() => {
  if (!restoreForm.value.filename) return null;
  return filteredBackupRecords.value.find(backup => backup.filename === restoreForm.value.filename);
});

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-';
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return timeStr;
  }
};

// 处理备份选择变化
const handleBackupChange = () => {
  // 可以在这里添加额外的逻辑
};

// 处理确认
const handleConfirm = () => {
  if (!restoreForm.value.filename) {
    return;
  }
  emit('confirm', { ...restoreForm.value });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    restoreForm.value = {
      filename: '',
      ...props.initialForm
    };
  }
});
</script>

<style scoped>
.progress-container {
  margin: 20px 0;
  text-align: center;
}

.progress-text {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.backup-option {
  padding: 4px 0;
}

.backup-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.backup-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.backup-type {
  background: #e1f3d8;
  color: #67c23a;
  padding: 2px 6px;
  border-radius: 3px;
}

.backup-tables {
  font-size: 12px;
  color: #606266;
}

.backup-info {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  flex: 1;
}
</style>
