from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import psycopg2
from psycopg2.extras import RealDictCursor
from .utils import get_db_connection

order_report_bp = Blueprint('order_report', __name__, url_prefix='/api')

@order_report_bp.route('/orders/report/statistics', methods=['GET'])
def get_order_statistics():
    """获取订单统计数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')
        order_no = request.args.get('order_no')

        # 构建查询条件
        where_conditions = []
        params = []

        if start_date:
            where_conditions.append('date >= %s')
            params.append(start_date)
        if end_date:
            where_conditions.append('date <= %s')
            params.append(end_date)
        if shop:
            where_conditions.append('shop = %s')
            params.append(shop)
        if order_no:
            where_conditions.append('order_no LIKE %s')
            params.append(f'%{order_no}%')

        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 查询订单统计数据
        order_sql = f"""
        SELECT
            COUNT(*) as total_orders,
            SUM(sale_price + box_fee + delivery_fee) as total_sales,
            SUM(sale_price - purchase_price) as product_profit,
            SUM(box_fee) as total_box_fee,
            SUM(delivery_fee) as total_delivery_fee,
            SUM(total_profit) as total_profit,
            SUM(purchase_price) as total_purchase_price,
            AVG(sale_price + box_fee + delivery_fee) as avg_order_value,
            AVG(total_profit) as avg_profit
        FROM orderlist
        WHERE isdelete = 0 AND ({where_clause})
        """

        cursor.execute(order_sql, params)
        order_result = cursor.fetchone()

        # 查询回款统计数据（根据相同的筛选条件）
        payment_where_conditions = []
        payment_params = []

        if start_date:
            payment_where_conditions.append('date >= %s')
            payment_params.append(start_date)
        if end_date:
            payment_where_conditions.append('date <= %s')
            payment_params.append(end_date)
        if shop:
            payment_where_conditions.append('shop = %s')
            payment_params.append(shop)

        payment_where_clause = ' AND '.join(payment_where_conditions) if payment_where_conditions else '1=1'

        payment_sql = f"""
        SELECT
            COALESCE(SUM(amount), 0) as total_payment_amount
        FROM paymentreceipt
        WHERE is_deleted = 0 AND ({payment_where_clause})
        """

        cursor.execute(payment_sql, payment_params)
        payment_result = cursor.fetchone()

        # 计算实际利润率
        total_purchase_price = float(order_result['total_purchase_price'] or 0)
        total_payment_amount = float(payment_result['total_payment_amount'] or 0)

        if total_purchase_price > 0:
            actual_profit_margin = ((total_payment_amount - total_purchase_price) / total_purchase_price) * 100
        else:
            actual_profit_margin = 0

        # 格式化结果，保留两位小数
        statistics = {
            'totalOrders': int(order_result['total_orders'] or 0),
            'totalSales': round(float(order_result['total_sales'] or 0), 2),
            'productProfit': round(float(order_result['product_profit'] or 0), 2),
            'totalBoxFee': round(float(order_result['total_box_fee'] or 0), 2),
            'totalDeliveryFee': round(float(order_result['total_delivery_fee'] or 0), 2),
            'totalProfit': round(float(order_result['total_profit'] or 0), 2),
            'totalPurchasePrice': round(total_purchase_price, 2),
            'totalPaymentAmount': round(total_payment_amount, 2),
            'avgOrderValue': round(float(order_result['avg_order_value'] or 0), 2),
            'avgProfit': round(float(order_result['avg_profit'] or 0), 2),
            'actualProfitMargin': round(actual_profit_margin, 2),
            'dateRange': {
                'startDate': start_date,
                'endDate': end_date
            }
        }

        cursor.close()
        conn.close()

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': statistics
        })

    except Exception as e:
        print(f"获取订单统计数据失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'获取订单统计数据失败: {str(e)}',
            'data': None
        }), 500

@order_report_bp.route('/orders/report/distribution', methods=['GET'])
def get_order_distribution():
    """获取订单分布数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')
        order_no = request.args.get('order_no')

        # 构建查询条件
        where_conditions = []
        params = []

        if start_date:
            where_conditions.append('date >= %s')
            params.append(start_date)
        if end_date:
            where_conditions.append('date <= %s')
            params.append(end_date)
        if shop:
            where_conditions.append('shop = %s')
            params.append(shop)
        if order_no:
            where_conditions.append('order_no LIKE %s')
            params.append(f'%{order_no}%')

        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 查询店铺销售分布
        shop_sales_sql = f"""
        SELECT
            shop as name,
            SUM(sale_price + box_fee + delivery_fee) as value,
            COUNT(*) as count
        FROM orderlist
        WHERE {where_clause}
        GROUP BY shop
        ORDER BY value DESC
        """

        cursor.execute(shop_sales_sql, params)
        shop_distribution = [
            {
                'name': row['name'],
                'value': round(float(row['value']), 2),
                'count': int(row['count'])
            }
            for row in cursor.fetchall()
        ]

        # 查询店铺利润分布
        shop_profit_sql = f"""
        SELECT
            shop as name,
            SUM(total_profit) as value,
            COUNT(*) as count
        FROM orderlist
        WHERE {where_clause}
        GROUP BY shop
        ORDER BY value DESC
        """

        cursor.execute(shop_profit_sql, params)
        profit_distribution = [
            {
                'name': row['name'],
                'value': round(float(row['value']), 2),
                'count': int(row['count'])
            }
            for row in cursor.fetchall()
        ]

        distribution_data = {
            'shopDistribution': shop_distribution,
            'profitDistribution': profit_distribution,
            'dateRange': {
                'startDate': start_date,
                'endDate': end_date
            }
        }

        cursor.close()
        conn.close()

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': distribution_data
        })

    except Exception as e:
        print(f"获取订单分布数据失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'获取订单分布数据失败: {str(e)}',
            'data': None
        }), 500

@order_report_bp.route('/orders/report/daily', methods=['GET'])
def get_order_daily_data():
    """获取订单每日数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')
        order_no = request.args.get('order_no')

        # 构建查询条件
        where_conditions = []
        params = []

        if start_date:
            where_conditions.append('date >= %s')
            params.append(start_date)
        if end_date:
            where_conditions.append('date <= %s')
            params.append(end_date)
        if shop:
            where_conditions.append('shop = %s')
            params.append(shop)
        if order_no:
            where_conditions.append('order_no LIKE %s')
            params.append(f'%{order_no}%')

        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 查询每日数据
        daily_sql = f"""
        SELECT
            shop,
            date,
            SUM(sale_price + box_fee + delivery_fee) as sales_amount,
            SUM(total_profit) as profit_amount,
            COUNT(*) as order_count
        FROM orderlist
        WHERE {where_clause}
        GROUP BY shop, date
        ORDER BY shop, date
        """

        cursor.execute(daily_sql, params)
        daily_results = cursor.fetchall()

        # 生成日期范围
        if start_date and end_date:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            dates = []
            current = start
            while current <= end:
                dates.append(current.strftime('%Y-%m-%d'))
                current += timedelta(days=1)
        else:
            dates = []

        # 组织数据
        shop_daily_data = {}
        for row in daily_results:
            shop_name = row['shop']
            date_str = row['date']  # date字段已经是字符串格式
            sales = round(float(row['sales_amount'] or 0), 2)
            profit = round(float(row['profit_amount'] or 0), 2)
            count = int(row['order_count'] or 0)

            if shop_name not in shop_daily_data:
                shop_daily_data[shop_name] = {
                    'salesData': {},
                    'profitData': {},
                    'orderCountData': {}
                }

            shop_daily_data[shop_name]['salesData'][date_str] = sales
            shop_daily_data[shop_name]['profitData'][date_str] = profit
            shop_daily_data[shop_name]['orderCountData'][date_str] = count

        # 转换为前端需要的格式
        shops = []
        for shop_name, data in shop_daily_data.items():
            shops.append({
                'shop': shop_name,
                'salesData': data['salesData'],
                'profitData': data['profitData'],
                'orderCountData': data['orderCountData']
            })

        daily_data = {
            'dates': dates,
            'shops': shops,
            'dateRange': {
                'startDate': start_date,
                'endDate': end_date
            }
        }

        cursor.close()
        conn.close()

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': daily_data
        })

    except Exception as e:
        print(f"获取订单每日数据失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'获取订单每日数据失败: {str(e)}',
            'data': None
        }), 500

@order_report_bp.route('/orders/report/detail', methods=['GET'])
def get_order_detail_data():
    """获取订单详细统计数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')
        order_no = request.args.get('order_no')

        # 构建查询条件
        where_conditions = []
        params = []

        if start_date:
            where_conditions.append('date >= %s')
            params.append(start_date)
        if end_date:
            where_conditions.append('date <= %s')
            params.append(end_date)
        if shop:
            where_conditions.append('o.shop = %s')
            params.append(shop)
        if order_no:
            where_conditions.append('order_no LIKE %s')
            params.append(f'%{order_no}%')

        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'

        # 构建回款查询条件（与订单查询条件相同）
        payment_where_conditions = []
        payment_params = []

        if start_date:
            payment_where_conditions.append('date >= %s')
            payment_params.append(start_date)
        if end_date:
            payment_where_conditions.append('date <= %s')
            payment_params.append(end_date)
        if shop:
            payment_where_conditions.append('shop = %s')
            payment_params.append(shop)

        payment_where_clause = ' AND '.join(payment_where_conditions) if payment_where_conditions else '1=1'

        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 查询店铺详细统计（关联回款数据）
        shop_detail_sql = f"""
        SELECT
            o.shop,
            COUNT(*) as order_count,
            SUM(o.sale_price + o.box_fee + o.delivery_fee) as total_sales,
            SUM(o.total_profit) as total_profit,
            SUM(o.actual_payment) as total_actual_payment,
            SUM(o.purchase_price) as total_purchase_price,
            COALESCE(p.total_payment_amount, 0) as total_payment_amount,
            AVG(o.sale_price + o.box_fee + o.delivery_fee) as avg_order_value,
            AVG(o.total_profit) as avg_profit,
            CASE
                WHEN SUM(o.sale_price + o.box_fee + o.delivery_fee) > 0 THEN (SUM(o.total_profit) / SUM(o.sale_price + o.box_fee + o.delivery_fee)) * 100
                ELSE 0
            END as profit_margin
        FROM orderlist o
        LEFT JOIN (
            SELECT
                shop,
                SUM(amount) as total_payment_amount
            FROM paymentreceipt
            WHERE is_deleted = 0 AND ({payment_where_clause})
            GROUP BY shop
        ) p ON o.shop = p.shop
        WHERE o.isdelete = 0 AND ({where_clause})
        GROUP BY o.shop, p.total_payment_amount
        ORDER BY total_sales DESC
        """

        # 合并参数：先是回款查询参数，再是订单查询参数
        all_params = payment_params + params
        cursor.execute(shop_detail_sql, all_params)
        shop_results = cursor.fetchall()

        # 格式化结果，保留两位小数
        shop_details = []
        for row in shop_results:
            shop_details.append({
                'shop': row['shop'],
                'orderCount': int(row['order_count'] or 0),
                'totalSales': round(float(row['total_sales'] or 0), 2),
                'totalProfit': round(float(row['total_profit'] or 0), 2),
                'totalActualPayment': round(float(row['total_actual_payment'] or 0), 2),
                'totalPurchasePrice': round(float(row['total_purchase_price'] or 0), 2),
                'totalPaymentAmount': round(float(row['total_payment_amount'] or 0), 2),
                'avgOrderValue': round(float(row['avg_order_value'] or 0), 2),
                'avgProfit': round(float(row['avg_profit'] or 0), 2),
                'profitMargin': round(float(row['profit_margin'] or 0), 2)
            })

        detail_data = {
            'shopDetails': shop_details,
            'dateRange': {
                'startDate': start_date,
                'endDate': end_date
            }
        }

        cursor.close()
        conn.close()

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': detail_data
        })

    except Exception as e:
        print(f"获取订单详细数据失败: {e}")
        return jsonify({
            'code': 500,
            'message': f'获取订单详细数据失败: {str(e)}',
            'data': None
        }), 500
