import { requestClient } from '#/api/request';
import { useAccessStore } from '@vben/stores';

// 表详情接口
export interface TableDetail {
  name: string;
  description: string;
  records: number;
  size: string;
  size_bytes: number;
}

// 备份统计信息接口
export interface BackupStats {
  table_count: number;
  total_records: number;
  total_size: string;
  table_details: TableDetail[];
}

// 获取备份统计信息
export async function fetchBackupStats() {
  const response = await requestClient.get<BackupStats>('/backup/stats', {
    responseReturn: 'body'
  });



  // 检查响应数据是否有效
  if (response && typeof response === 'object') {
    // 如果响应包含 success 字段，说明是包装格式
    if ('success' in response && 'data' in response) {
      const wrappedResponse = response as any;
      if (wrappedResponse.success && wrappedResponse.data) {
        return wrappedResponse.data;
      } else {
        throw new Error(wrappedResponse.message || '获取备份统计信息失败');
      }
    } else {
      // 直接返回的数据格式
      return response;
    }
  } else {
    throw new Error('API响应数据格式错误');
  }
}

// 获取表名映射（从API获取动态映射）
export async function fetchTableNameMapping(): Promise<Record<string, string>> {
  try {
    const stats = await fetchBackupStats();
    const mapping: Record<string, string> = {};

    // 从table_details中提取表名和描述的映射
    stats.table_details.forEach(table => {
      mapping[table.name] = table.description;
    });

    return mapping;
  } catch (error) {
    console.error('获取表名映射失败:', error);
    // 返回空映射，使用表名本身
    return {};
  }
}

// 备份表单接口
export interface BackupForm {
  name: string;
  type: 'full' | 'partial';
  format: 'custom'; // 固定使用.backup格式
  tables: string[];
}

// 备份记录接口
export interface BackupRecord {
  filename: string;
  backup_name: string;
  backup_type: 'full' | 'partial';
  tables: string[];
  size: string;
  size_bytes: number;
  status: 'completed' | 'failed' | 'in_progress';
  created_at: string;
  error?: string;
}

// 创建备份
export async function createBackup(backupForm: BackupForm) {
  return requestClient.post('/backup/create', backupForm);
}

// 获取备份列表
export async function fetchBackupList() {
  return requestClient.get<BackupRecord[]>('/backup/list');
}

// 下载备份文件
export async function downloadBackup(filename: string) {
  try {
    // 获取当前的访问token
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    if (!token) {
      throw new Error('未找到访问令牌，请重新登录');
    }

    // 使用原生fetch来避免响应拦截器的问题
    const response = await fetch(`/api/backup/download/${filename}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();

    // 检查blob大小
    if (blob.size === 0) {
      throw new Error('下载的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    // 添加到DOM并触发下载
    document.body.appendChild(link);

    // 使用setTimeout确保链接已添加到DOM
    setTimeout(() => {
      link.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
    }, 10);

    return {
      success: true,
      filename: filename,
      size: blob.size
    };
  } catch (error) {
    console.error('下载备份文件失败:', error);
    throw error;
  }
}

// 删除备份文件
export async function deleteBackup(filename: string) {
  return requestClient.delete(`/backup/delete/${filename}`);
}

// 获取备份详细信息
export async function fetchBackupInfo(filename: string) {
  return requestClient.get<BackupRecord>(`/backup/info/${filename}`, {
    responseReturn: 'body'
  });
}

// 恢复表单接口
export interface RestoreForm {
  filename: string;
}

// 恢复备份
export async function restoreBackup(restoreForm: RestoreForm) {
  return requestClient.post('/backup/restore', restoreForm);
}
