-- 创建PostgreSQL数据库脚本
-- 请在PostgreSQL命令行或pgAdmin中执行此脚本

-- 方案1：使用template0和zh-CN排序规则（根据您的系统配置）
CREATE DATABASE postgresql_vue_db
    WITH
    TEMPLATE = template0
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'zh-CN'
    LC_CTYPE = 'zh-CN'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 如果上面的命令报错，请使用方案2（使用template0和C排序规则）：
-- CREATE DATABASE postgresql_vue_db
--     WITH
--     TEMPLATE = template0
--     OWNER = postgres
--     ENCODING = 'UTF8'
--     LC_COLLATE = 'C'
--     LC_CTYPE = 'C'
--     TABLESPACE = pg_default
--     CONNECTION LIMIT = -1;

-- 如果仍然报错，请使用最简单的方案3：
-- CREATE DATABASE postgresql_vue_db;

-- 连接到新创建的数据库
\c postgresql_vue_db;

-- 创建扩展（如果需要）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 数据库创建完成
-- 现在可以运行 python backend/database/init_database.py 来初始化表结构和数据
