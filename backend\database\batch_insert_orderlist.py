import psycopg2
import pandas as pd
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def batch_insert_from_excel(file_path):
    """从Excel文件批量导入订单数据到PostgreSQL数据库"""
    try:
        # 连接到PostgreSQL数据库
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()

        # 验证orderlist表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'orderlist'
            )
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            raise ValueError("数据库中不存在orderlist表")

        print(f"成功连接到PostgreSQL数据库，找到orderlist表")
        
        # 读取Excel文件的Sheet2工作表
        df = pd.read_excel(file_path, sheet_name='Sheet2')
        
        # 确保列名匹配数据库字段（忽略大小写）
        df.columns = df.columns.str.strip().str.lower()
        
        # 映射Excel列名到数据库字段名（不包含id字段，让数据库自动生成）
        column_mapping = {
            '日期': 'date',
            '店铺': 'shop',
            '订单编号': 'order_no',
            '销售价': 'sale_price',
            '餐盒费': 'box_fee',
            '配送费': 'delivery_fee',
            '实付': 'actual_payment',
            '买入价': 'purchase_price',
            '商品盈利': 'product_profit',
            '总盈利': 'total_profit',
        }
        
        # 重命名DataFrame列以匹配数据库字段
        df = df.rename(columns=column_mapping)
        
        # 将日期列转换为YYYY-MM-DD格式的字符串
        if 'date' in df.columns:
            df['date'] = df['date'].dt.strftime('%Y-%m-%d')
        
        # 添加isdelete字段（默认值0，表示未删除）
        df['isdelete'] = 0

        # 提取要插入的列（不包含id，让数据库自动生成）
        columns_to_insert = [
            'date', 'shop', 'order_no', 'sale_price',
            'box_fee', 'delivery_fee', 'actual_payment',
            'purchase_price', 'product_profit', 'total_profit',
             'isdelete'
        ]

        # 转换为记录列表（每个记录是一个元组）
        records = df[columns_to_insert].values.tolist()

        # 使用 executemany 批量插入（PostgreSQL使用%s占位符，不包含id字段）
        sql = """
        INSERT INTO orderlist
        (date, shop, order_no, sale_price, box_fee,
        delivery_fee, actual_payment, purchase_price,
        product_profit, total_profit, isdelete)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        cursor.executemany(sql, records)

        # 重置序列计数器到正确的值，避免主键冲突
        cursor.execute("SELECT setval('orderlist_id_seq', (SELECT MAX(id) FROM orderlist))")

        conn.commit()

        print(f"成功从Excel导入 {len(records)} 条订单数据")
        print("已重置ID序列计数器，避免主键冲突")

    except psycopg2.Error as e:
        print(f"PostgreSQL错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if conn:
            conn.close()
            print("数据库连接已关闭")

# 使用示例
if __name__ == "__main__":
    excel_file = "订单数据.xlsx"  # 替换为你的Excel文件路径

    batch_insert_from_excel(excel_file)