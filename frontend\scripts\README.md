# Frontend Scripts

这个目录包含前端项目的构建和开发工具脚本。

## 📁 目录结构

```
frontend/scripts/
├── turbo-run/          # Turbo 运行工具
│   ├── src/           # 源码 (已在Git中)
│   ├── bin/           # 可执行文件 (已在Git中)
│   ├── dist/          # 编译产物 (被Git忽略)
│   └── package.json   # 配置文件
├── vsh/               # VSH 开发工具
│   ├── src/           # 源码 (已在Git中)
│   ├── bin/           # 可执行文件 (已在Git中)
│   ├── dist/          # 编译产物 (被Git忽略)
│   └── package.json   # 配置文件
└── README.md          # 本文件
```

## 🚀 自动构建机制

### 为什么 `dist/` 文件夹被忽略？

`dist/` 文件夹包含编译后的代码，其中包含**机器特定的绝对路径**，例如：

```javascript
// 在不同电脑上会生成不同的路径
"@vben/turbo-run": "C:/Users/<USER>/Desktop/postgresql-vue-project/frontend/scripts/turbo-run"
```

这会导致在不同开发环境中产生Git冲突。

### 自动构建流程

项目使用了**自动构建机制**来解决这个问题：

1. **安装依赖时自动构建**：
   ```json
   // frontend/package.json
   "postinstall": "pnpm -r run stub --if-present"
   ```

2. **每个脚本的构建命令**：
   ```json
   // scripts/*/package.json
   "scripts": {
     "stub": "pnpm unbuild --stub"
   }
   ```

## 📋 新设备开发流程

### 1. Clone 项目
```bash
git clone <repository-url>
cd postgresql-vue-project
```

### 2. 安装依赖（自动构建脚本）
```bash
cd frontend
pnpm install
# 这会自动执行 postinstall 钩子，构建所有脚本的 dist/ 文件夹
```

### 3. 验证脚本可用
```bash
# 测试 turbo-run
pnpm turbo-run --help

# 测试 vsh
pnpm vsh --help

# 启动开发服务器
pnpm dev
```

## 🔧 手动构建（如果需要）

如果自动构建失败，可以手动构建：

```bash
# 构建所有脚本
cd frontend
pnpm -r run stub

# 或者单独构建
cd frontend/scripts/turbo-run
pnpm stub

cd ../vsh
pnpm stub
```

## ⚠️ 注意事项

1. **不要手动修改 `dist/` 文件夹**：这些文件是自动生成的
2. **不要提交 `dist/` 文件夹到Git**：已在 `.gitignore` 中忽略
3. **如果脚本无法运行**：检查是否正确执行了 `pnpm install`
4. **源码修改后**：需要重新运行 `pnpm stub` 来重新构建

## 🎯 这种方案的优势

- ✅ **避免Git冲突**：不同电脑不会产生路径冲突
- ✅ **自动化构建**：新设备clone后自动可用
- ✅ **保持同步**：源码修改后可以重新构建
- ✅ **开发友好**：不需要手动管理编译产物

## 🛠️ 相关工具

- **unbuild**: 用于构建TypeScript脚本
- **jiti**: 运行时TypeScript执行器
- **turbo**: Monorepo构建工具
- **vsh**: 项目开发工具集
