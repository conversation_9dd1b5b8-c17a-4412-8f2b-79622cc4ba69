from flask import Blueprint, request, jsonify
from .utils import get_db_connection, format_response, validate_required_fields
from .auth import token_required, check_permission
from utils.datetime_utils import format_datetime_list


financepayment_bp = Blueprint('financepayment', __name__, url_prefix='/api')

@financepayment_bp.route('/financepayment', methods=['GET'])
@token_required
@check_permission('MENU_FINANCEPAYMENT')
def get_financepayment_list():
    """获取回款登记列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        date = request.args.get('date')
        shop = request.args.get('shop')
        category = request.args.get('category')
        
        # 构建查询条件
        where_conditions = ['is_deleted = 0']
        params = []
        
        # 日期条件
        if date:
            where_conditions.append('record_date = %s')
            params.append(date)
        elif start_date and end_date:
            where_conditions.append('record_date >= %s AND record_date <= %s')
            params.extend([start_date, end_date])
        
        # 店铺条件
        if shop:
            where_conditions.append('shop LIKE %s')
            params.append(f'%{shop}%')
        
        # 分类条件
        if category:
            where_conditions.append('category LIKE %s')
            params.append(f'%{category}%')
        
        # 构建WHERE子句
        where_clause = ' AND '.join(where_conditions)
        
        # 获取总数
        count_query = f'SELECT COUNT(*) FROM financepayment WHERE {where_clause}'
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f'''
            SELECT id, shop, record_date, category, amount, created_at, updated_at, screenshot
            FROM financepayment 
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_query, params + [page_size, offset])
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 转换为字典列表
        results = []
        for row in cursor.fetchall():
            row_dict = dict(zip(columns, row))
            results.append(row_dict)
        
        # 格式化时间字段
        results = format_datetime_list(results, ['created_at', 'updated_at'])
        
        return format_response(
            success=True,
            data=results,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        print(f"获取回款登记列表错误: {str(e)}")
        return format_response(success=False, message="获取回款登记列表失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment', methods=['POST'])
@token_required
@check_permission('FINANCEPAYMENT_ADD')
def create_financepayment():
    """创建回款登记记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'category', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 插入回款登记记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            INSERT INTO financepayment (shop, record_date, category, amount, screenshot, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        ''', (
            data['shop'],
            data['record_date'],
            data['category'],
            data.get('amount', 0),
            screenshot_path,
            now,
            now
        ))

        financepayment_id = cursor.fetchone()[0]
        conn.commit()

        return format_response(
            success=True,
            data={'id': financepayment_id, **data},
            message="回款登记记录创建成功"
        )

    except Exception as e:
        print(f"创建回款登记记录错误: {str(e)}")
        return format_response(success=False, message="创建回款登记记录失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/<int:financepayment_id>', methods=['PUT'])
@token_required
@check_permission('FINANCEPAYMENT_EDIT')
def update_financepayment(financepayment_id):
    """更新回款登记记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'category', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款登记记录是否存在
        cursor.execute('SELECT COUNT(*) FROM financepayment WHERE id = %s AND is_deleted = 0', (financepayment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款登记记录不存在")

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 更新回款登记记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            UPDATE financepayment
            SET shop = %s, record_date = %s, category = %s, amount = %s,
                screenshot = %s, updated_at = %s
            WHERE id = %s
        ''', (
            data['shop'],
            data['record_date'],
            data['category'],
            data.get('amount', 0),
            screenshot_path,
            now,
            financepayment_id
        ))

        conn.commit()

        return format_response(
            success=True,
            data={'id': financepayment_id, **data},
            message="回款登记记录更新成功"
        )

    except Exception as e:
        print(f"更新回款登记记录错误: {str(e)}")
        return format_response(success=False, message="更新回款登记记录失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/<int:financepayment_id>', methods=['DELETE'])
@token_required
@check_permission('FINANCEPAYMENT_DELETE')
def delete_financepayment(financepayment_id):
    """删除回款登记记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款登记记录是否存在
        cursor.execute('SELECT COUNT(*) FROM financepayment WHERE id = %s AND is_deleted = 0', (financepayment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款登记记录不存在")

        # 软删除
        cursor.execute('UPDATE financepayment SET is_deleted = 1 WHERE id = %s', (financepayment_id,))
        conn.commit()

        return format_response(success=True, message="删除成功")

    except Exception as e:
        print(f"删除回款登记记录错误: {str(e)}")
        return format_response(success=False, message="删除失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/batch-delete', methods=['POST'])
@token_required
@check_permission('FINANCEPAYMENT_BATCH_DELETE')
def batch_delete_financepayment():
    """批量删除回款登记记录"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要删除的回款登记记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量软删除
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'UPDATE financepayment SET is_deleted = 1 WHERE id IN ({placeholders})', ids)
        conn.commit()

        return format_response(success=True, message="批量删除成功")

    except Exception as e:
        print(f"批量删除回款登记记录错误: {str(e)}")
        return format_response(success=False, message="批量删除失败")
    finally:
        conn.close()
