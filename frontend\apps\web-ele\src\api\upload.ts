import { requestClient } from '#/api/request';

// 上传响应接口
export interface UploadResponse {
  url: string;
  filename: string;
  original_name: string;
}

// 批量上传响应接口
export interface BatchUploadResponse {
  uploaded: UploadResponse[];
  failed: Array<{
    filename: string;
    error: string;
  }>;
  total: number;
  success_count: number;
  failed_count: number;
}

/**
 * 上传单个图片
 */
export async function uploadImage(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post<{ data: UploadResponse }>('/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 批量上传图片
 */
export async function uploadImages(files: File[]) {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });

  return requestClient.post<{ data: BatchUploadResponse }>('/upload/images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 删除图片
 */
export async function deleteImage(path: string) {
  return requestClient.post('/upload/delete', { path });
}
