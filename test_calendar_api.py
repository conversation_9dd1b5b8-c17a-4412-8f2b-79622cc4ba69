#!/usr/bin/env python3
"""
测试订单日历API的脚本
"""

import requests
import json
from datetime import datetime, timedelta

# 配置
BASE_URL = "http://127.0.0.1:5000/api"
USERNAME = "admin"  # 替换为实际用户名
PASSWORD = "123456"  # 替换为实际密码

def login():
    """登录获取token"""
    login_url = f"{BASE_URL}/login"
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(login_url, json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            return result['data']['token']
    
    print(f"登录失败: {response.text}")
    return None

def test_calendar_api(token):
    """测试日历API"""
    # 计算当前月份的开始和结束日期
    now = datetime.now()
    start_date = now.replace(day=1).strftime('%Y-%m-%d')
    
    # 下个月第一天减一天就是本月最后一天
    if now.month == 12:
        next_month = now.replace(year=now.year + 1, month=1, day=1)
    else:
        next_month = now.replace(month=now.month + 1, day=1)
    
    end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
    
    print(f"测试日期范围: {start_date} 到 {end_date}")
    
    # 测试API
    calendar_url = f"{BASE_URL}/orders/calendar"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    params = {
        "start_date": start_date,
        "end_date": end_date
    }
    
    print(f"请求URL: {calendar_url}")
    print(f"请求参数: {params}")
    
    response = requests.get(calendar_url, headers=headers, params=params)
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            if result.get('success'):
                print("✅ API测试成功!")
                data = result.get('data', {})
                print(f"返回的店铺数量: {len(data)}")
                
                for shop, days in data.items():
                    print(f"店铺: {shop}, 数据天数: {len(days)}")
                    if days:
                        # 显示前几天的数据作为示例
                        for day in days[:3]:
                            print(f"  日期: {day['date']}, 订单: {day['orderCount']}, 销售: {day['totalSales']}, 净利: {day['netProfit']}")
                        if len(days) > 3:
                            print(f"  ... 还有 {len(days) - 3} 天的数据")
            else:
                print(f"❌ API返回失败: {result.get('message')}")
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON格式")
    else:
        print(f"❌ API请求失败，状态码: {response.status_code}")

def test_with_specific_shops(token):
    """测试指定店铺的API"""
    print("\n--- 测试指定店铺 ---")
    
    # 先获取店铺列表
    stores_url = f"{BASE_URL}/online-stores"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(stores_url, headers=headers)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            shops = [store['name'] for store in result['data']]
            print(f"可用店铺: {shops}")
            
            if shops:
                # 测试第一个店铺
                test_shop = shops[0]
                print(f"测试店铺: {test_shop}")
                
                now = datetime.now()
                start_date = now.replace(day=1).strftime('%Y-%m-%d')
                if now.month == 12:
                    next_month = now.replace(year=now.year + 1, month=1, day=1)
                else:
                    next_month = now.replace(month=now.month + 1, day=1)
                end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
                
                calendar_url = f"{BASE_URL}/orders/calendar"
                params = {
                    "start_date": start_date,
                    "end_date": end_date,
                    "shops": [test_shop]
                }
                
                response = requests.get(calendar_url, headers=headers, params=params)
                print(f"指定店铺响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("✅ 指定店铺API测试成功!")
                        data = result.get('data', {})
                        print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"❌ 指定店铺API返回失败: {result.get('message')}")

def main():
    print("=== 订单日历API测试 ===")
    
    # 登录
    print("1. 登录...")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print("✅ 登录成功")
    
    # 测试基本API
    print("\n2. 测试基本日历API...")
    test_calendar_api(token)
    
    # 测试指定店铺API
    print("\n3. 测试指定店铺API...")
    test_with_specific_shops(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
