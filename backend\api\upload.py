#!/usr/bin/env python3
"""文件上传API"""

import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from PIL import Image
from .auth import token_required
from .utils import format_response

upload_bp = Blueprint('upload', __name__, url_prefix='/api')

# 允许的图片格式
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_dir():
    """确保上传目录存在"""
    upload_dir = os.path.join(os.getcwd(), 'static', 'uploads')
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    return upload_dir

def convert_to_webp(image_path, output_path, quality=85):
    """将图片转换为webp格式"""
    try:
        with Image.open(image_path) as img:
            # 如果图片有透明通道，转换为RGB
            if img.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 保存为webp格式
            img.save(output_path, 'WEBP', quality=quality, optimize=True)
            return True
    except Exception as e:
        print(f"转换图片格式失败: {str(e)}")
        return False

@upload_bp.route('/upload/image', methods=['POST'])
@token_required
def upload_image():
    """上传图片"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return format_response(success=False, message="没有选择文件")
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return format_response(success=False, message="没有选择文件")
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return format_response(success=False, message="不支持的文件格式，请上传图片文件")
        
        # 确保上传目录存在
        upload_dir = ensure_upload_dir()
        
        # 生成唯一文件名
        file_ext = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_ext}"
        temp_path = os.path.join(upload_dir, unique_filename)
        
        # 保存临时文件
        file.save(temp_path)
        
        # 生成webp文件名
        webp_filename = f"{uuid.uuid4().hex}.webp"
        webp_path = os.path.join(upload_dir, webp_filename)
        
        # 转换为webp格式
        if convert_to_webp(temp_path, webp_path):
            # 删除临时文件
            os.remove(temp_path)
            
            # 返回相对路径
            relative_path = f"/static/uploads/{webp_filename}"
            
            return format_response(
                success=True,
                data={
                    "url": relative_path,
                    "filename": webp_filename,
                    "original_name": file.filename
                },
                message="图片上传成功"
            )
        else:
            # 转换失败，删除临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return format_response(success=False, message="图片格式转换失败")
            
    except Exception as e:
        print(f"上传图片错误: {str(e)}")
        return format_response(success=False, message="图片上传失败")

@upload_bp.route('/upload/images', methods=['POST'])
@token_required
def upload_multiple_images():
    """批量上传图片"""
    try:
        files = request.files.getlist('files')
        
        if not files or len(files) == 0:
            return format_response(success=False, message="没有选择文件")
        
        uploaded_files = []
        failed_files = []
        
        # 确保上传目录存在
        upload_dir = ensure_upload_dir()
        
        for file in files:
            if file.filename == '':
                continue
                
            if not allowed_file(file.filename):
                failed_files.append({
                    "filename": file.filename,
                    "error": "不支持的文件格式"
                })
                continue
            
            try:
                # 生成唯一文件名
                file_ext = file.filename.rsplit('.', 1)[1].lower()
                unique_filename = f"{uuid.uuid4().hex}.{file_ext}"
                temp_path = os.path.join(upload_dir, unique_filename)
                
                # 保存临时文件
                file.save(temp_path)
                
                # 生成webp文件名
                webp_filename = f"{uuid.uuid4().hex}.webp"
                webp_path = os.path.join(upload_dir, webp_filename)
                
                # 转换为webp格式
                if convert_to_webp(temp_path, webp_path):
                    # 删除临时文件
                    os.remove(temp_path)
                    
                    # 返回相对路径
                    relative_path = f"/static/uploads/{webp_filename}"
                    
                    uploaded_files.append({
                        "url": relative_path,
                        "filename": webp_filename,
                        "original_name": file.filename
                    })
                else:
                    # 转换失败，删除临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    failed_files.append({
                        "filename": file.filename,
                        "error": "图片格式转换失败"
                    })
                    
            except Exception as e:
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return format_response(
            success=True,
            data={
                "uploaded": uploaded_files,
                "failed": failed_files,
                "total": len(files),
                "success_count": len(uploaded_files),
                "failed_count": len(failed_files)
            },
            message=f"批量上传完成，成功{len(uploaded_files)}个，失败{len(failed_files)}个"
        )
        
    except Exception as e:
        print(f"批量上传图片错误: {str(e)}")
        return format_response(success=False, message="批量上传失败")

@upload_bp.route('/upload/delete', methods=['POST'])
@token_required
def delete_image():
    """删除图片"""
    try:
        data = request.get_json()
        image_path = data.get('path')
        
        if not image_path:
            return format_response(success=False, message="图片路径不能为空")
        
        # 构建完整文件路径
        if image_path.startswith('/static/uploads/'):
            filename = image_path.replace('/static/uploads/', '')
            full_path = os.path.join(os.getcwd(), 'static', 'uploads', filename)
            
            if os.path.exists(full_path):
                os.remove(full_path)
                return format_response(success=True, message="图片删除成功")
            else:
                return format_response(success=False, message="图片文件不存在")
        else:
            return format_response(success=False, message="无效的图片路径")
            
    except Exception as e:
        print(f"删除图片错误: {str(e)}")
        return format_response(success=False, message="删除图片失败")
