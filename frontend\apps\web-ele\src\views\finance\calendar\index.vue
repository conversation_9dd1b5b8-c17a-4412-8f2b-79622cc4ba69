<template>
  <div class="calendar-container">
    <!-- 店铺选择区域 -->
    <el-card class="shop-selector">
      <div class="shop-buttons">
        <el-button
          :type="selectedShops.length === allShops.length ? 'primary' : ''"
          @click="toggleAllShops"
          :disabled="allShops.length === 0"
        >
          全选 ({{ allShops.length }})
        </el-button>
        <el-button
          v-for="shop in allShops"
          :key="shop"
          :type="selectedShops.includes(shop) ? 'primary' : ''"
          @click="toggleShop(shop)"
        >
          {{ shop }}
        </el-button>
        <div v-if="allShops.length === 0" class="no-shops">
          暂无店铺数据，请先添加店铺
        </div>
      </div>
    </el-card>

    <!-- 日历区域 -->
    <el-card class="calendar-wrapper" v-loading="loading" element-loading-text="加载收支数据...">
      <el-calendar v-model="currentDate" class="finance-calendar">
        <template #date-cell="{ data }">
          <div class="calendar-cell" :class="{ 'other-month': data.type !== 'current-month' }">
            <div class="date-number">{{ data.day.split('-').pop() }}</div>
            <div v-if="getDayData(data.day)" class="day-stats">
              <div class="stat-item income">
                <span class="label">收入</span>
                <span class="value">¥{{ (getDayData(data.day)?.income || 0).toFixed(2) }}</span>
              </div>
              <div class="stat-item expense">
                <span class="label">支出</span>
                <span class="value">¥{{ (getDayData(data.day)?.expense || 0).toFixed(2) }}</span>
              </div>
              <div class="stat-item balance" :class="{ 'positive': (getDayData(data.day)?.balance || 0) > 0, 'negative': (getDayData(data.day)?.balance || 0) < 0 }">
                <span class="label">结余</span>
                <span class="value">¥{{ (getDayData(data.day)?.balance || 0).toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { ElCalendar, ElButton, ElMessage, ElCard } from 'element-plus';
import { fetchOfflineStores, fetchFinanceList } from '#/api/finance';
import dayjs from 'dayjs';

defineOptions({ name: 'FinanceCalendar' });

// 收支日历数据类型
interface FinanceDayData {
  date: string;
  income: number;
  expense: number;
  balance: number;
}

interface FinanceCalendarData {
  [shop: string]: FinanceDayData[];
}

// 获取收支日历数据
const fetchFinanceCalendarData = async (params: {
  start_date: string;
  end_date: string;
  shops?: string[];
}) => {
  try {
    // 获取财务记录数据
    const financeResponse = await fetchFinanceList({
      start_date: params.start_date,
      end_date: params.end_date,
      shop: params.shops?.join(','), // 如果API支持多店铺查询
      pageSize: 10000 // 获取所有数据
    });

    const financeData = financeResponse.data || [];
    const calendarData: FinanceCalendarData = {};

    // 初始化店铺数据
    if (params.shops) {
      params.shops.forEach(shop => {
        calendarData[shop] = [];
      });
    }

    // 按日期和店铺分组统计收支
    const dailyStats: { [key: string]: { [shop: string]: { income: number; expense: number } } } = {};

    financeData.forEach((record: any) => {
      const date = record.record_date;
      const shop = record.shop;
      const amount = parseFloat(record.amount) || 0;
      const type = record.type;

      if (!dailyStats[date]) {
        dailyStats[date] = {};
      }
      if (!dailyStats[date][shop]) {
        dailyStats[date][shop] = { income: 0, expense: 0 };
      }

      if (type === '收入') {
        dailyStats[date][shop].income += amount;
      } else if (type === '支出') {
        dailyStats[date][shop].expense += amount;
      }
    });

    // 转换为日历数据格式
    Object.keys(dailyStats).forEach(date => {
      const dateStats = dailyStats[date];
      if (dateStats) {
        Object.keys(dateStats).forEach(shop => {
          if (calendarData[shop]) {
            const stats = dateStats[shop];
            if (stats) {
              const balance = stats.income - stats.expense;

              calendarData[shop].push({
                date,
                income: Math.round(stats.income * 100) / 100,
                expense: Math.round(stats.expense * 100) / 100,
                balance: Math.round(balance * 100) / 100
              });
            }
          }
        });
      }
    });

    return { data: calendarData };
  } catch (error) {
    console.error('获取收支日历数据失败:', error);
    return { data: {} };
  }
};

// 响应式数据
const currentDate = ref(new Date());
const allShops = ref<string[]>([]);
const selectedShops = ref<string[]>([]);
const calendarData = ref<FinanceCalendarData>({});
const loading = ref(false);

// 计算属性：日历显示范围的开始和结束日期（包括跨月显示的日期）
const currentMonthRange = computed(() => {
  const currentMonth = dayjs(currentDate.value);

  // 获取当前月第一天是星期几（0=周日，1=周一...）
  const firstDayOfMonth = currentMonth.startOf('month');
  const firstDayWeekday = firstDayOfMonth.day();

  // 获取当前月最后一天是星期几
  const lastDayOfMonth = currentMonth.endOf('month');
  const lastDayWeekday = lastDayOfMonth.day();

  // 计算日历显示的实际开始日期（可能是上个月的日期）
  const start = firstDayOfMonth.subtract(firstDayWeekday, 'day').format('YYYY-MM-DD');

  // 计算日历显示的实际结束日期（可能是下个月的日期）
  const end = lastDayOfMonth.add(6 - lastDayWeekday, 'day').format('YYYY-MM-DD');

  return { start, end };
});

// 获取指定日期的数据
const getDayData = (date: string): FinanceDayData | null => {
  if (selectedShops.value.length === 0) return null;

  // 合并所有选中店铺的数据
  let totalIncome = 0;
  let totalExpense = 0;

  selectedShops.value.forEach(shop => {
    const shopData = calendarData.value[shop];
    if (shopData) {
      const dayData = shopData.find(item => item.date === date);
      if (dayData) {
        totalIncome += dayData.income;
        totalExpense += dayData.expense;
      }
    }
  });

  // 计算结余
  const balance = totalIncome - totalExpense;

  if (totalIncome === 0 && totalExpense === 0) {
    return null;
  }

  return {
    date,
    income: totalIncome,
    expense: totalExpense,
    balance
  };
};

// 移除formatNumber函数，直接使用toFixed(2)显示金额

// 切换店铺选择
const toggleShop = (shop: string) => {
  const index = selectedShops.value.indexOf(shop);
  if (index > -1) {
    selectedShops.value.splice(index, 1);
  } else {
    selectedShops.value.push(shop);
  }
};

// 全选/取消全选
const toggleAllShops = () => {
  if (selectedShops.value.length === allShops.value.length) {
    selectedShops.value = [];
  } else {
    selectedShops.value = [...allShops.value];
  }
};

// 获取店铺列表
const fetchShops = async () => {
  try {
    // 使用财务模块的线下店铺数据
    const response = await fetchOfflineStores();
    //console.log('店铺API响应:', response);

    // 尝试多种数据格式，参考其他组件的处理方式
    let storeData = null;
    if (Array.isArray(response)) {
      storeData = response;
    } else if (response && Array.isArray(response.data)) {
      storeData = response.data;
    }

    if (storeData) {
      // 只显示启用的店铺
      const filteredStores = storeData.filter(store => store.status === 1);
      allShops.value = filteredStores.map(shop => shop.name);
      // 默认选择所有店铺
      selectedShops.value = [...allShops.value];
      //console.log('获取到的店铺:', allShops.value);
    } else {
      console.error('店铺数据格式错误:', response);
      ElMessage.error('店铺数据格式错误');
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error);
    ElMessage.error('获取店铺列表失败');
  }
};

// 获取日历数据
const fetchData = async () => {
  // 暂时移除店铺筛选，先测试基本功能
  // if (selectedShops.value.length === 0) {
  //   calendarData.value = {};
  //   return;
  // }

  loading.value = true;
  try {
    const response = await fetchFinanceCalendarData({
      start_date: currentMonthRange.value.start,
      end_date: currentMonthRange.value.end,
      shops: selectedShops.value.length > 0 ? selectedShops.value : undefined
    });

    calendarData.value = response.data || {};

  } catch (error: any) {
    ElMessage.error('获取日历数据失败');
  } finally {
    loading.value = false;
  }
};

// 监听日期变化
watch(currentDate, () => {
  fetchData();
});

// 监听选中店铺变化
watch(selectedShops, () => {
  fetchData();
}, { deep: true });

// 组件挂载时初始化
onMounted(async () => {
  await fetchShops();
  await fetchData();
});

</script>

<style scoped lang="scss">
.calendar-container {
  padding: 10px;
  height: calc(100vh - 90px); /* 减去tab栏和header的高度 */
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
}

.shop-selector {
  flex-shrink: 0;

  .shop-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .no-shops {
    color: #909399;
    font-size: 14px;
    padding: 8px 12px;
    text-align: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px dashed #dcdfe6;
  }
}

.calendar-wrapper {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  :deep(.el-calendar) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-calendar__body {
      flex: 1;
      overflow: auto;
    }
  }
}

.calendar-cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .date-number {
    position: absolute;
    top: 2px;
    left: 4px;
    font-size: 14px;
    font-weight: 600;
    /*color: #303133;*/
    z-index: 1;
  }

  &.other-month {
    .date-number {
      color: #8c8e91;
    }
  }

  .day-stats {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
    font-size: 10px;
    padding-top: 20px; // 为左上角日期留出空间

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1px 4px;
      border-radius: 3px;
      background-color: rgba(0, 0, 0, 0.05);

      .label {
        /*color: #606266;*/
        font-weight: 500;
        font-size: 9px;
        min-width: 20px;
      }

      .value {
        font-weight: 600;
      }

      &.income {
        background-color: rgba(103, 194, 58, 0.1);
        .value {
          color: #67c23a;
        }
      }

      &.expense {
        background-color: rgba(245, 108, 108, 0.1);
        .value {
          color: #f56c6c;
        }
      }

      &.balance {
        background-color: rgba(144, 147, 153, 0.1);
        .value {
          color: #909399;
        }

        &.positive {
          background-color: rgba(103, 194, 58, 0.1);
          .value {
            color: #67c23a;
          }
        }

        &.negative {
          background-color: rgba(245, 108, 108, 0.1);
          .value {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .calendar-container {
    padding: 8px;
    gap: 8px;
  }

  .shop-selector {
    padding: 12px;

    .shop-buttons {
      gap: 6px;

      .el-button {
        font-size: 11px;
        padding: 6px 10px;
      }
    }
  }

  .calendar-wrapper {
    padding: 12px;
  }

  .calendar-cell {
    .date-number {
      font-size: 12px;
      margin-bottom: 4px;
    }

    .day-stats {
      font-size: 9px;
      gap: 2px;

      .stat-item {
        padding: 1px 3px;
      }
    }
  }
}
</style>
