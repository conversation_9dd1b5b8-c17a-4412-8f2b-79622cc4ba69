<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  El<PERSON>ard,
  ElRow,
  ElCol
} from 'element-plus';
import { VbenCountToAnimator } from '@vben/common-ui';
import type { OrderStatisticsData } from '../../../../api/order-stat';

defineOptions({ name: 'OrderStatisticsCards' });

interface Props {
  statisticsData: OrderStatisticsData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 动画key，用于重新触发动画
const animationKey = ref(0);

// 计算统计数据，提供默认值
const statistics = computed(() => {
  if (!props.statisticsData) {
    return {
      totalOrders: 0,
      totalSales: 0,
      productProfit: 0,
      totalBoxFee: 0,
      totalDeliveryFee: 0,
      totalProfit: 0,
      totalPurchasePrice: 0,
      totalPaymentAmount: 0,
      avgOrderValue: 0,
      avgProfit: 0,
      actualProfitMargin: 0
    };
  }
  return props.statisticsData;
});

// 计算总盈利（总回款金额 - 总买入价）
const totalActualProfit = computed(() => {
  return statistics.value.totalPaymentAmount - statistics.value.totalPurchasePrice;
});

// 计算净利润率（基于净利润）
const actualProfitMargin = computed(() => {
  const totalSales = statistics.value.totalSales;
  if (totalSales > 0) {
    return (totalActualProfit.value / totalSales) * 100;
  }
  return 0;
});

// 监听数据变化，触发动画
watch(() => props.statisticsData, () => {
  animationKey.value++;
}, { deep: true });
</script>

<template>
  <div class="statistics-cards">
    <!-- 第一行：6个卡片 -->
    <ElRow :gutter="16" class="mb-4">
      <!-- 订单总数 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">订单总数</div>
            <div class="card-value orders">
              <VbenCountToAnimator
                :key="`orders-${animationKey}`"
                :end-val="statistics.totalOrders"
                :start-val="0"
                :decimals="0"
                suffix="单"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 销售总额 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">销售总额</div>
            <div class="card-value sales">
              <VbenCountToAnimator
                :key="`sales-${animationKey}`"
                :end-val="statistics.totalSales"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 商品利润 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">商品利润</div>
            <div class="card-value product-profit">
              <VbenCountToAnimator
                :key="`product-profit-${animationKey}`"
                :end-val="statistics.productProfit"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 总餐盒费 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">总餐盒费</div>
            <div class="card-value box-fee">
              <VbenCountToAnimator
                :key="`box-fee-${animationKey}`"
                :end-val="statistics.totalBoxFee"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 总配送费 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">总配送费</div>
            <div class="card-value delivery-fee">
              <VbenCountToAnimator
                :key="`delivery-fee-${animationKey}`"
                :end-val="statistics.totalDeliveryFee"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 利润总额 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">利润总额</div>
            <div class="card-value profit">
              <VbenCountToAnimator
                :key="`profit-${animationKey}`"
                :end-val="statistics.totalProfit"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 第二行：6个卡片 -->
    <ElRow :gutter="16">
      <!-- 平均客单价 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">平均客单价</div>
            <div class="card-value avg-order">
              <VbenCountToAnimator
                :key="`avg-order-${animationKey}`"
                :end-val="statistics.avgOrderValue"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 平均利润 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">平均利润</div>
            <div class="card-value avg-profit">
              <VbenCountToAnimator
                :key="`avg-profit-${animationKey}`"
                :end-val="statistics.avgProfit"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 总买入价 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">总买入价</div>
            <div class="card-value purchase">
              <VbenCountToAnimator
                :key="`purchase-${animationKey}`"
                :end-val="statistics.totalPurchasePrice"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 回款总额 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">回款总额</div>
            <div class="card-value payment">
              <VbenCountToAnimator
                :key="`payment-${animationKey}`"
                :end-val="statistics.totalPaymentAmount"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      <!-- 净利润 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">净利润</div>
            <div class="card-value actual-profit">
              <VbenCountToAnimator
                :key="`actual-profit-${animationKey}`"
                :end-val="totalActualProfit"
                :start-val="0"
                :decimals="2"
                prefix="¥"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
      
      <!-- 利润率 -->
      <ElCol :xs="12" :sm="8" :md="4" :lg="4" :xl="4">
        <ElCard class="statistic-card" v-loading="loading">
          <div class="card-content">
            <div class="card-title">净利润率</div>
            <div class="card-value margin">
              <VbenCountToAnimator
                :key="`margin-${animationKey}`"
                :end-val="actualProfitMargin"
                :start-val="0"
                :decimals="2"
                suffix="%"
                :duration="1500"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped>
.statistics-cards {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .statistics-cards :deep(.el-col) {
    margin-bottom: 16px;
  }
}

.statistic-card {
  height: 120px;
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  cursor: pointer;
}

.statistic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: hsl(var(--primary));
}

.statistic-card :deep(.el-card__body) {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-title {
  font-size: 18px;
  margin-bottom: 12px;
  font-weight: 500;
  text-align: left;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
}

.card-value.orders {
  color: #409EFF;
}

.card-value.sales {
  color: #67C23A;
}

.card-value.product-profit {
  color: #9C27B0;
}

.card-value.box-fee {
  color: #FF5722;
}

.card-value.delivery-fee {
  color: #795548;
}

.card-value.profit {
  color: #E6A23C;
}

.card-value.purchase {
  color: #909399;
}

.card-value.payment {
  color: #00BCD4;
}

.card-value.actual-profit {
  color: #F56C6C;
}

.card-value.avg-order {
  color: #9C27B0;
}

.card-value.avg-profit {
  color: #607D8B;
}

.card-value.margin {
  color: #FF9800;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistic-card {
    height: 100px;
    margin-bottom: 12px;
  }
  
  .statistic-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .card-value {
    font-size: 20px;
  }
  
  .card-title {
    font-size: 13px;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .statistic-card {
    height: 90px;
    margin-bottom: 8px;
  }
  
  .statistic-card :deep(.el-card__body) {
    padding: 12px;
  }
  
  .card-value {
    font-size: 18px;
  }
  
  .card-title {
    font-size: 12px;
    margin-bottom: 6px;
  }
}

/* 夜间模式适配 */
@media (prefers-color-scheme: dark) {
  .statistic-card {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }
  
  .statistic-card:hover {
    border-color: hsl(var(--primary));
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
  }
}
</style>
