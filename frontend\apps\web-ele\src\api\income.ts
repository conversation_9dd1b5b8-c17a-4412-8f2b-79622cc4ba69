import { requestClient } from '#/api/request';

export interface IncomeCategory {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface IncomeSearchParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: string;
}

export interface IncomeCreateData {
  name: string;
  status: number;
}

export interface IncomeUpdateData {
  name: string;
  status: number;
}

// 获取收入分类列表
export async function fetchIncomeList(params: IncomeSearchParams) {
  return requestClient.get<{
    data: IncomeCategory[];
    total: number;
    page: number;
    pageSize: number;
  }>('/income-categories', {
    params,
    responseReturn: 'body'
  });
}

// 创建收入分类
export async function addIncome(data: IncomeCreateData) {
  return requestClient.post<{
    data: IncomeCategory;
    message: string;
  }>('/income-categories', data);
}

// 更新收入分类
export async function updateIncome(id: number, data: IncomeUpdateData) {
  return requestClient.put<{
    message: string;
  }>(`/income-categories/${id}`, data);
}

// 删除收入分类
export async function deleteIncome(id: number) {
  return requestClient.delete<{
    message: string;
  }>(`/income-categories/${id}`);
}

// 批量删除收入分类
export async function batchDeleteIncome(ids: number[]) {
  return requestClient.post<{
    message: string;
  }>('/income-categories/batch-delete', { ids });
}

// 更新收入分类状态
export async function updateIncomeStatus(id: number, status: boolean) {
  return requestClient.put<{
    message: string;
  }>(`/income-categories/${id}/status`, { status: status ? 1 : 0 });
}
