import { requestClient } from '#/api/request';

// 回款搜索参数接口
export interface PaymentSearchParams {
  page?: number;
  pageSize?: number;
  date?: string;
  start_date?: string;
  end_date?: string;
  shop?: string;
}

// 回款数据接口
export interface Payment {
  id?: number;
  date: string;
  shop: string;
  amount: number;
}

/**
 * 获取回款列表
 */
export async function fetchPaymentList(params: PaymentSearchParams) {
  return requestClient.get<{
    data: Payment[];
    total: number;
    page: number;
    pageSize: number;
  }>('/payments', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 添加回款记录
 */
export async function addPayment(data: Payment) {
  return requestClient.post<{ data: Payment }>('/payments', data);
}

/**
 * 更新回款记录
 */
export async function updatePayment(id: number, data: Payment) {
  return requestClient.put<{ data: Payment }>(`/payments/${id}`, data);
}

/**
 * 删除回款记录
 */
export async function deletePayment(id: number) {
  return requestClient.delete(`/payments/${id}`);
}

/**
 * 批量删除回款记录
 */
export async function batchDeletePayment(ids: number[]) {
  return requestClient.post('/payments/batch-delete', { ids });
}

/**
 * 获取线上店铺选项（用于下拉选择）
 */
export async function fetchOnlineStoreOptions() {
  return requestClient.get<{
    data: Array<{ id: number; name: string; status: number }>;
  }>('/online-stores');
}
