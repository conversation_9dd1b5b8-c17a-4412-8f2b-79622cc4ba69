import psycopg2
import psycopg2.extras
from datetime import datetime
from config.config import Config

class Permission:
    """权限模型类"""
    
    def __init__(self):
        self.db_config = Config.DATABASE_CONFIG
    
    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(**self.db_config)
        return conn
    
    def get_all_permissions(self):
        """获取所有权限（树形结构）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT id, code, name, type, description, parent_code
            FROM permissions
            ORDER BY type, code
        ''')
        
        permissions = cursor.fetchall()
        conn.close()
        
        # 转换为字典列表
        permission_list = [dict(perm) for perm in permissions]
        
        # 构建树形结构
        permission_tree = []
        permission_map = {}
        
        # 先创建所有节点的映射
        for perm in permission_list:
            perm['children'] = []
            permission_map[perm['code']] = perm
        
        # 构建父子关系
        for perm in permission_list:
            if perm['parent_code'] and perm['parent_code'] in permission_map:
                permission_map[perm['parent_code']]['children'].append(perm)
            else:
                permission_tree.append(perm)
        
        return permission_tree
    
    def get_role_permissions(self, role_id):
        """获取角色的权限列表"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT p.id, p.code, p.name, p.type, p.description, p.parent_code
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = %s
            ORDER BY p.type, p.code
        ''', (role_id,))
        
        permissions = cursor.fetchall()
        conn.close()
        
        return [dict(perm) for perm in permissions]
    
    def get_user_permissions(self, user_id):
        """获取用户的所有权限（通过角色）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT DISTINCT p.id, p.code, p.name, p.type, p.description, p.parent_code
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s
            ORDER BY p.type, p.code
        ''', (user_id,))
        
        permissions = cursor.fetchall()
        conn.close()
        
        return [dict(perm) for perm in permissions]
    
    def get_user_permission_codes(self, user_id):
        """获取用户的权限码列表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT DISTINCT p.code
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s
        ''', (user_id,))

        codes = cursor.fetchall()
        conn.close()

        return [code[0] for code in codes]
    
    def assign_permissions_to_role(self, role_id, permission_ids):
        """为角色分配权限"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查角色是否存在
            cursor.execute('SELECT id FROM roles WHERE id = %s AND is_deleted = 0', (role_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '角色不存在'}
            
            # 删除角色现有权限
            cursor.execute('DELETE FROM role_permissions WHERE role_id = %s', (role_id,))
            
            # 分配新权限
            for permission_id in permission_ids:
                # 检查权限是否存在
                cursor.execute('SELECT id FROM permissions WHERE id = %s', (permission_id,))
                if cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO role_permissions (role_id, permission_id, created_at)
                        VALUES (%s, %s, %s)
                    ''', (role_id, permission_id, datetime.now()))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': '权限分配成功'}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'权限分配失败: {str(e)}'}

    def get_user_menu_permissions(self, user_id):
        """获取用户的菜单权限"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT DISTINCT p.id, p.code, p.name, p.type, p.description, p.parent_code
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s AND p.type = 'menu'
            ORDER BY p.code
        ''', (user_id,))
        
        permissions = cursor.fetchall()
        conn.close()
        
        return [dict(perm) for perm in permissions]
    
    def check_user_permission(self, user_id, permission_code):
        """检查用户是否有指定权限"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*)
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = %s AND p.code = %s
        ''', (user_id, permission_code))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
    
    def get_permissions_by_codes(self, codes):
        """根据权限码获取权限信息"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        if not codes:
            return []
        
        placeholders = ','.join(['%s' for _ in codes])
        cursor.execute(f'''
            SELECT id, code, name, type, description, parent_code
            FROM permissions
            WHERE code IN ({placeholders})
            ORDER BY type, code
        ''', codes)
        
        permissions = cursor.fetchall()
        conn.close()
        
        return [dict(perm) for perm in permissions]
    
    def get_permission_by_code(self, code):
        """根据权限码获取权限信息"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT id, code, name, type, description, parent_code
            FROM permissions
            WHERE code = %s
        ''', (code,))
        
        permission = cursor.fetchone()
        conn.close()
        
        if permission:
            return dict(permission)
        return None

    def get_role_permission_ids(self, role_id):
        """获取角色的权限ID列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT permission_id
            FROM role_permissions
            WHERE role_id = %s
        ''', (role_id,))
        
        permission_ids = cursor.fetchall()
        conn.close()
        
        return [pid[0] for pid in permission_ids]
