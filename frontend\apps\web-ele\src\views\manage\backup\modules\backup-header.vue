<template>
  <ElCard class="header-card">
    <div class="header-content">
      <div class="header-info">
        <h2>
          <ElIcon><DataBoard /></ElIcon>
          数据备份与恢复
        </h2>
        <p>管理系统数据的备份和恢复，确保数据安全</p>
      </div>
      <div class="header-actions">
        <ElSpace>
          <ElButton type="primary" :icon="Document" @click="$emit('openBackup')">
            创建备份
          </ElButton>
          <ElButton type="success" :icon="Upload" @click="$emit('openRestore')">
            恢复数据
          </ElButton>
          <ElButton :icon="Refresh" @click="$emit('refresh')" :loading="loading">
            刷新统计
          </ElButton>
        </ElSpace>
      </div>
    </div>
  </ElCard>
</template>

<script setup lang="ts">
import {
  ElCard,
  ElButton,
  ElSpace,
  ElIcon
} from 'element-plus';
import {
  Upload,
  Refresh,
  DataBoard,
  Document
} from '@element-plus/icons-vue';

interface Props {
  loading?: boolean;
}

defineProps<Props>();

defineEmits<{
  openBackup: [];
  openRestore: [];
  refresh: [];
}>();
</script>

<style scoped>
/* 页面头部 */
.header-card {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h2 {
  margin: 0 0 8px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
