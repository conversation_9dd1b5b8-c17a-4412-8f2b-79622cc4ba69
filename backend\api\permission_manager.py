from flask import Blueprint, request, jsonify, g
from utils.response import ResponseUtils
from api.auth import token_required
from permission_manager import PermissionManager

permission_manager_bp = Blueprint('permission_manager', __name__, url_prefix='/api/permission-manager')

@permission_manager_bp.route('/menu', methods=['POST'])
@token_required
def add_menu_permission():
    """添加菜单权限接口"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['code', 'name', 'description']
        for field in required_fields:
            if not data.get(field):
                return ResponseUtils.bad_request(f"{field}不能为空")
        
        code = data['code'].strip()
        name = data['name'].strip()
        description = data['description'].strip()
        parent_code = data.get('parent_code', '').strip() or None
        
        manager = PermissionManager()
        result = manager.add_menu_permission(code, name, description, parent_code)
        
        if result['success']:
            return ResponseUtils.success({'permission_id': result['permission_id']}, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])
            
    except Exception as e:
        print(f"添加菜单权限错误: {str(e)}")
        return ResponseUtils.error("添加菜单权限失败", 500)

@permission_manager_bp.route('/button', methods=['POST'])
@token_required
def add_button_permission():
    """添加按钮权限接口"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['code', 'name', 'description', 'parent_code']
        for field in required_fields:
            if not data.get(field):
                return ResponseUtils.bad_request(f"{field}不能为空")
        
        code = data['code'].strip()
        name = data['name'].strip()
        description = data['description'].strip()
        parent_code = data['parent_code'].strip()
        
        manager = PermissionManager()
        result = manager.add_button_permission(code, name, description, parent_code)
        
        if result['success']:
            return ResponseUtils.success({'permission_id': result['permission_id']}, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])
            
    except Exception as e:
        print(f"添加按钮权限错误: {str(e)}")
        return ResponseUtils.error("添加按钮权限失败", 500)

@permission_manager_bp.route('/module', methods=['POST'])
@token_required
def add_module_permissions():
    """批量添加模块权限接口"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['module_name', 'module_code']
        for field in required_fields:
            if not data.get(field):
                return ResponseUtils.bad_request(f"{field}不能为空")
        
        module_name = data['module_name'].strip()
        module_code = data['module_code'].strip()
        parent_code = data.get('parent_code', '').strip() or None
        buttons = data.get('buttons', ['add', 'edit', 'delete', 'batchDelete', 'view'])
        
        manager = PermissionManager()
        results = manager.add_module_permissions(module_name, module_code, parent_code, buttons)
        
        # 统计成功和失败的数量
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        return ResponseUtils.success({
            'results': results,
            'success_count': success_count,
            'total_count': total_count
        }, f"批量添加完成，成功 {success_count}/{total_count}")
            
    except Exception as e:
        print(f"批量添加模块权限错误: {str(e)}")
        return ResponseUtils.error("批量添加模块权限失败", 500)

@permission_manager_bp.route('/list', methods=['GET'])
@token_required
def list_permissions():
    """获取权限列表接口"""
    try:
        perm_type = request.args.get('type')  # menu, button 或 None
        
        manager = PermissionManager()
        permissions = manager.list_permissions(perm_type)
        
        return ResponseUtils.success(permissions, "获取权限列表成功")
            
    except Exception as e:
        print(f"获取权限列表错误: {str(e)}")
        return ResponseUtils.error("获取权限列表失败", 500)

@permission_manager_bp.route('/delete/<code>', methods=['DELETE'])
@token_required
def delete_permission(code):
    """删除权限接口"""
    try:
        manager = PermissionManager()
        result = manager.delete_permission(code)
        
        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])
            
    except Exception as e:
        print(f"删除权限错误: {str(e)}")
        return ResponseUtils.error("删除权限失败", 500)

@permission_manager_bp.route('/assign-role', methods=['POST'])
@token_required
def assign_permissions_to_role():
    """为角色分配权限接口"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('role_id') or not data.get('permission_codes'):
            return ResponseUtils.bad_request("role_id和permission_codes不能为空")
        
        role_id = int(data['role_id'])
        permission_codes = data['permission_codes']
        
        if not isinstance(permission_codes, list):
            return ResponseUtils.bad_request("permission_codes必须是数组")
        
        manager = PermissionManager()
        result = manager.assign_permissions_to_role(role_id, permission_codes)
        
        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])
            
    except Exception as e:
        print(f"分配权限错误: {str(e)}")
        return ResponseUtils.error("分配权限失败", 500)
