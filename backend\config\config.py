import os
from datetime import timedelta

class Config:
    """应用配置类"""

    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # PostgreSQL数据库配置
    DATABASE_CONFIG = {
        'host': os.environ.get('DB_HOST') or 'localhost',
        'port': os.environ.get('DB_PORT') or '5432',
        'database': os.environ.get('DB_NAME') or 'postgresql_vue_db',
        'user': os.environ.get('DB_USER') or 'postgres',
        'password': os.environ.get('DB_PASSWORD') or '123456'
    }

    # 数据库连接字符串
    DATABASE_URL = f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # CORS配置 - 支持局域网访问
    CORS_ORIGINS = [
        "http://localhost:5777",
        "http://127.0.0.1:5777",
        "http://localhost:5778",
        "http://127.0.0.1:5778",
        # 局域网访问支持 - 请根据实际情况调整IP段
        "http://192.168.1.*:5777",  # 局域网前端访问
        "http://192.168.0.*:5777",  # 另一个常见的局域网段
        "http://10.0.0.*:5777",     # 企业网络常用段
        # 或者使用通配符允许所有来源（仅开发环境）
        "*"
    ]

    # 调试模式
    DEBUG = True
