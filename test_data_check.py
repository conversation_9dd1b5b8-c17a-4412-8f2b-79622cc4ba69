#!/usr/bin/env python3
"""
检查数据库中的测试数据
"""

import psycopg2
from datetime import datetime, timedelta

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'postgresql_vue_project',
    'user': 'postgres',
    'password': '123456',
    'client_encoding': 'utf8'
}

def check_data():
    """检查数据库中的数据"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 数据库数据检查 ===")
        
        # 检查店铺数据
        cursor.execute("SELECT COUNT(*) FROM online_stores WHERE isdelete = 0")
        store_count = cursor.fetchone()[0]
        print(f"线上店铺数量: {store_count}")
        
        if store_count > 0:
            cursor.execute("SELECT name, status FROM online_stores WHERE isdelete = 0 LIMIT 5")
            stores = cursor.fetchall()
            print("店铺列表:")
            for store in stores:
                print(f"  - {store[0]} (状态: {store[1]})")
        
        # 检查订单数据
        cursor.execute("SELECT COUNT(*) FROM orderlist WHERE isdelete = 0")
        order_count = cursor.fetchone()[0]
        print(f"\n订单数量: {order_count}")
        
        if order_count > 0:
            cursor.execute("""
                SELECT date, shop, COUNT(*), SUM(sale_price) 
                FROM orderlist 
                WHERE isdelete = 0 
                GROUP BY date, shop 
                ORDER BY date DESC 
                LIMIT 10
            """)
            orders = cursor.fetchall()
            print("最近订单统计:")
            for order in orders:
                print(f"  - {order[0]} {order[1]}: {order[2]}单, ¥{order[3]}")
        
        # 检查回款数据
        cursor.execute("SELECT COUNT(*) FROM paymentreceipt WHERE is_deleted = 0")
        payment_count = cursor.fetchone()[0]
        print(f"\n回款记录数量: {payment_count}")
        
        if payment_count > 0:
            cursor.execute("""
                SELECT date, shop, SUM(amount) 
                FROM paymentreceipt 
                WHERE is_deleted = 0 
                GROUP BY date, shop 
                ORDER BY date DESC 
                LIMIT 10
            """)
            payments = cursor.fetchall()
            print("最近回款记录:")
            for payment in payments:
                print(f"  - {payment[0]} {payment[1]}: ¥{payment[2]}")
        
        # 检查当前月份的数据
        now = datetime.now()
        start_date = now.replace(day=1).strftime('%Y-%m-%d')
        if now.month == 12:
            next_month = now.replace(year=now.year + 1, month=1, day=1)
        else:
            next_month = now.replace(month=now.month + 1, day=1)
        end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
        
        print(f"\n当前月份 ({start_date} 到 {end_date}) 数据:")
        
        cursor.execute("""
            SELECT COUNT(*) FROM orderlist 
            WHERE isdelete = 0 AND date >= %s AND date <= %s
        """, (start_date, end_date))
        current_month_orders = cursor.fetchone()[0]
        print(f"本月订单数量: {current_month_orders}")
        
        cursor.execute("""
            SELECT COUNT(*) FROM paymentreceipt 
            WHERE is_deleted = 0 AND date >= %s AND date <= %s
        """, (start_date, end_date))
        current_month_payments = cursor.fetchone()[0]
        print(f"本月回款数量: {current_month_payments}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据失败: {str(e)}")

def create_test_data():
    """创建一些测试数据"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("\n=== 创建测试数据 ===")
        
        # 检查是否已有店铺
        cursor.execute("SELECT COUNT(*) FROM online_stores WHERE isdelete = 0")
        if cursor.fetchone()[0] == 0:
            print("创建测试店铺...")
            cursor.execute("""
                INSERT INTO online_stores (name, status, isdelete, created_at, updated_at)
                VALUES 
                ('测试店铺A', 1, 0, NOW(), NOW()),
                ('测试店铺B', 1, 0, NOW(), NOW())
            """)
        
        # 创建本月的测试订单
        now = datetime.now()
        test_dates = [
            now.replace(day=1),
            now.replace(day=2),
            now.replace(day=3),
            now.replace(day=5),
            now.replace(day=8),
        ]
        
        for i, test_date in enumerate(test_dates):
            date_str = test_date.strftime('%Y-%m-%d')
            
            # 检查是否已有该日期的订单
            cursor.execute("""
                SELECT COUNT(*) FROM orderlist 
                WHERE date = %s AND isdelete = 0
            """, (date_str,))
            
            if cursor.fetchone()[0] == 0:
                print(f"创建 {date_str} 的测试订单...")
                
                # 为每个店铺创建订单
                shops = ['测试店铺A', '测试店铺B']
                for shop in shops:
                    for j in range(2):  # 每个店铺每天2个订单
                        cursor.execute("""
                            INSERT INTO orderlist 
                            (date, shop, order_no, sale_price, actual_payment, isdelete)
                            VALUES (%s, %s, %s, %s, %s, 0)
                        """, (
                            date_str,
                            shop,
                            f'TEST{date_str.replace("-", "")}{shop[-1]}{j+1}',
                            100 + i * 10 + j * 5,
                            100 + i * 10 + j * 5
                        ))
                
                # 创建对应的回款记录（第二天）
                payment_date = test_date + timedelta(days=1)
                payment_date_str = payment_date.strftime('%Y-%m-%d')
                
                for shop in shops:
                    cursor.execute("""
                        INSERT INTO paymentreceipt 
                        (date, shop, amount, is_deleted)
                        VALUES (%s, %s, %s, 0)
                    """, (
                        payment_date_str,
                        shop,
                        200 + i * 20  # 每个店铺的回款
                    ))
        
        conn.commit()
        conn.close()
        print("测试数据创建完成!")
        
    except Exception as e:
        print(f"创建测试数据失败: {str(e)}")

if __name__ == "__main__":
    check_data()
    
    # 询问是否创建测试数据
    create_test = input("\n是否创建测试数据? (y/n): ").lower().strip()
    if create_test == 'y':
        create_test_data()
        print("\n重新检查数据:")
        check_data()
