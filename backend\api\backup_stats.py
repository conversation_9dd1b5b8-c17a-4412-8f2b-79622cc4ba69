#!/usr/bin/env python3
"""备份统计API"""

from flask import Blueprint, jsonify
import psycopg2
from .auth import token_required, check_permission
from .utils import get_db_connection, format_response

backup_stats_bp = Blueprint('backup_stats', __name__, url_prefix='/api')

@backup_stats_bp.route('/backup/stats', methods=['GET'])
@token_required
@check_permission('MENU_BACKUP')
def get_backup_stats():
    """获取备份统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 定义需要统计的表
        tables = [
            'users', 'roles', 'permissions', 'user_roles', 'role_permissions',
            'orderlist', 'paymentreceipt', 'finance', 'financepayment',
            'income_categories', 'expenditure_categories', 
            'online_stores', 'offline_stores'
        ]
        
        stats = {
            'table_count': 0,
            'total_records': 0,
            'total_size': '0 KB',
            'table_details': []
        }
        
        total_records = 0
        total_size_bytes = 0
        existing_tables = []
        
        for table in tables:
            try:
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    )
                """, (table,))
                
                table_exists = cursor.fetchone()[0]
                
                if table_exists:
                    existing_tables.append(table)
                    
                    # 获取记录数
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    record_count = cursor.fetchone()[0]
                    total_records += record_count
                    
                    # 获取表大小
                    cursor.execute("""
                        SELECT pg_total_relation_size(%s::regclass)
                    """, (table,))

                    size_result = cursor.fetchone()
                    table_size_bytes = size_result[0] if size_result and size_result[0] else 0
                    total_size_bytes += table_size_bytes
                    
                    # 格式化表大小
                    table_size = format_bytes(table_size_bytes)
                    
                    # 获取表描述
                    table_description = get_table_description(table)
                    
                    stats['table_details'].append({
                        'name': table,
                        'description': table_description,
                        'records': record_count,
                        'size': table_size,
                        'size_bytes': table_size_bytes
                    })
                    
            except Exception as e:
                continue
        
        stats['table_count'] = len(existing_tables)
        stats['total_records'] = total_records
        stats['total_size'] = format_bytes(total_size_bytes)
        
        conn.close()
        


        return format_response(
            success=True,
            data=stats,
            message="获取备份统计信息成功"
        )
        
    except Exception as e:
        return format_response(
            success=False,
            message=f"获取备份统计信息失败: {str(e)}"
        )
    finally:
        if 'conn' in locals():
            conn.close()

def format_bytes(bytes_size):
    """格式化字节大小"""
    if bytes_size == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while bytes_size >= 1024 and i < len(size_names) - 1:
        bytes_size /= 1024.0
        i += 1
    
    if i == 0:
        return f"{int(bytes_size)} {size_names[i]}"
    else:
        return f"{bytes_size:.1f} {size_names[i]}"

def get_table_description(table_name):
    """获取表的描述信息，优先从数据库注释获取，否则使用默认映射"""
    try:
        # 首先尝试从数据库获取表注释
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT obj_description(c.oid, 'pg_class') as comment
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relname = %s AND n.nspname = 'public'
        """, (table_name,))

        result = cursor.fetchone()
        if result and result[0]:
            conn.close()
            return result[0]

        conn.close()
    except Exception as e:
        print(f"获取表注释失败: {str(e)}")

    # 如果没有数据库注释，使用默认映射
    descriptions = {
        'users': '用户账号信息',
        'roles': '系统角色配置',
        'permissions': '权限配置',
        'user_roles': '用户角色关联',
        'role_permissions': '角色权限关联',
        'orderlist': '订单记录',
        'paymentreceipt': '回款记录',
        'finance': '收支记录',
        'financepayment': '回款登记',
        'income_categories': '收入分类',
        'expenditure_categories': '支出分类',
        'online_stores': '线上店铺',
        'offline_stores': '线下店铺'
    }
    return descriptions.get(table_name, f'{table_name}表')
