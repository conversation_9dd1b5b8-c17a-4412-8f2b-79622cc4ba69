import { requestClient } from '#/api/request';

export interface OnlineStore {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface OnlineStoreSearchParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: string;
}

export interface OnlineStoreCreateData {
  name: string;
  status: number;
}

export interface OnlineStoreUpdateData {
  name: string;
  status: number;
}

// 获取线上店铺列表
export async function fetchOnlineStoreList(params: OnlineStoreSearchParams) {
  return requestClient.get<{
    data: OnlineStore[];
    total: number;
    page: number;
    pageSize: number;
  }>('/online-stores', {
    params,
    responseReturn: 'body'
  });
}

// 创建线上店铺
export async function addOnlineStore(data: OnlineStoreCreateData) {
  return requestClient.post<{
    data: OnlineStore;
    message: string;
  }>('/online-stores', data);
}

// 更新线上店铺
export async function updateOnlineStore(id: number, data: OnlineStoreUpdateData) {
  return requestClient.put<{
    message: string;
  }>(`/online-stores/${id}`, data);
}

// 删除线上店铺
export async function deleteOnlineStore(id: number) {
  return requestClient.delete<{
    message: string;
  }>(`/online-stores/${id}`);
}

// 批量删除线上店铺
export async function batchDeleteOnlineStore(ids: number[]) {
  return requestClient.post<{
    message: string;
  }>('/online-stores/batch-delete', { ids });
}

// 更新线上店铺状态
export async function updateOnlineStoreStatus(id: number, status: boolean) {
  return requestClient.put<{
    message: string;
  }>(`/online-stores/${id}/status`, { status: status ? 1 : 0 });
}

// 获取所有启用的线上店铺（用于下拉选择）
export async function fetchOnlineStores() {
  return requestClient.get<{
    data: OnlineStore[];
  }>('/online-stores', {
    responseReturn: 'body',
  });
}
