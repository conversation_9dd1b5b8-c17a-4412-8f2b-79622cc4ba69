<script setup lang="ts">
import { computed } from 'vue';
import {
  ElCard,
  ElTable,
  ElTableColumn
} from 'element-plus';
import type { OrderDetailData } from '../../../../api/order-stat';

defineOptions({ name: 'OrderDetailTable' });

interface Props {
  detailData: OrderDetailData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 订单详细统计表格数据
const orderDetailTableData = computed(() => {
  if (!props.detailData || !props.detailData.shopDetails) {
    return [];
  }

  return props.detailData.shopDetails;
});
</script>

<template>
  <div class="order-detail-table">
    <ElCard class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">店铺详细统计</span>
          <span class="record-count">共 {{ orderDetailTableData.length }} 个店铺</span>
        </div>
      </template>

      <div class="table-container">
        <ElTable
          :data="orderDetailTableData"
          v-loading="loading"
          border
          style="width: 100%"
          :height="400"
          class="modern-table"
        >
          <ElTableColumn
            prop="shop"
            label="店铺名称"
            min-width="120"
            fixed="left"
            header-align="center"
            align="center"
            show-overflow-tooltip
          />
          <ElTableColumn
            prop="orderCount"
            label="订单数量"
            min-width="100"
            align="center"
            header-align="center"
            sortable
          />
          <ElTableColumn
            prop="totalSales"
            label="销售总额"
            min-width="100"
            align="center"
            header-align="center"
            sortable
          >
            <template #default="{ row }">
              <span style="color: #67C23A; font-weight: bold;">
                ¥{{ row.totalSales.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="totalProfit"
            label="利润总额"
            min-width="120"
            align="center"
            header-align="center"
            sortable
          >
            <template #default="{ row }">
              <span :style="{ color: row.totalProfit >= 0 ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">
                ¥{{ row.totalProfit.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="totalActualPayment"
            label="实付总额"
            width="120"
            align="center"
            header-align="center"
            sortable
          >
            <template #default="{ row }">
              <span style="color: #409EFF; font-weight: bold;">
                ¥{{ row.totalActualPayment.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="avgOrderValue"
            label="平均客单价"
            min-width="120"
            align="center"
            header-align="center"
            sortable
          >
            <template #default="{ row }">
              <span style="color: #409EFF; font-weight: bold;">
                ¥{{ row.avgOrderValue.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="avgProfit"
            label="平均利润"
            width="120"
            align="center"
            header-align="center"
            sortable
          >
            <template #default="{ row }">
              <span :style="{ color: row.avgProfit >= 0 ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">
                ¥{{ row.avgProfit.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            label="净利润率"
            min-width="100"
            align="center"
            header-align="center"
            sortable
            :sort-method="(a, b) => {
              const aMargin = a.totalSales > 0 ? ((a.totalPaymentAmount - a.totalPurchasePrice) / a.totalSales) * 100 : 0;
              const bMargin = b.totalSales > 0 ? ((b.totalPaymentAmount - b.totalPurchasePrice) / b.totalSales) * 100 : 0;
              return aMargin - bMargin;
            }"
          >
            <template #default="{ row }">
              <span
                :style="{
                  color: ((row.totalPaymentAmount - row.totalPurchasePrice) / row.totalSales * 100) >= 0 ? '#67C23A' : '#F56C6C',
                  fontWeight: '500'
                }"
              >
                {{ row.totalSales > 0 ? (((row.totalPaymentAmount - row.totalPurchasePrice) / row.totalSales) * 100).toFixed(2) : '0.00' }}%
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            label="净利润"
            min-width="120"
            align="center"
            header-align="center"
            sortable
            :sort-method="(a, b) => (a.totalPaymentAmount - a.totalPurchasePrice) - (b.totalPaymentAmount - b.totalPurchasePrice)"
          >
            <template #default="{ row }">
              <span
                :style="{
                  color: (row.totalPaymentAmount - row.totalPurchasePrice) >= 0 ? '#67C23A' : '#F56C6C',
                  fontWeight: 'bold'
                }"
              >
                ¥{{ (row.totalPaymentAmount - row.totalPurchasePrice).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
              </span>
            </template>
          </ElTableColumn>
          <template #empty>
            <div class="empty-state">
              <div class="empty-icon">📊</div>
              <div class="empty-text">暂无店铺统计数据</div>
              <div class="empty-description">请先添加订单记录以查看统计信息</div>
            </div>
          </template>
        </ElTable>
      </div>

      <!-- 统计汇总 -->
      <div class="table-summary">
        <div class="summary-item">
          <span class="summary-label">总订单:</span>
          <span class="summary-value">
            {{ orderDetailTableData.reduce((sum, item) => sum + item.orderCount, 0) }}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">总销售:</span>
          <span class="summary-value income">
            ¥{{ orderDetailTableData.reduce((sum, item) => sum + item.totalSales, 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">利润总额:</span>
          <span
            class="summary-value"
            :style="{
              color: (orderDetailTableData.reduce((sum, item) => sum + item.totalProfit, 0)) >= 0 ? '#67C23A' : '#F56C6C'
            }"
          >
            ¥{{ (orderDetailTableData.reduce((sum, item) => sum + item.totalProfit, 0)).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">净利润:</span>
          <span
            class="summary-value"
            :style="{
              color: (orderDetailTableData.reduce((sum, item) => sum + (item.totalPaymentAmount - item.totalPurchasePrice), 0)) >= 0 ? '#67C23A' : '#F56C6C',
              fontWeight: 'bold'
            }"
          >
            ¥{{ (orderDetailTableData.reduce((sum, item) => sum + (item.totalPaymentAmount - item.totalPurchasePrice), 0)).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) }}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">净利润率:</span>
          <span
            class="summary-value"
            :style="{
              color: (() => {
                const totalSales = orderDetailTableData.reduce((sum, item) => sum + item.totalSales, 0);
                const netProfit = orderDetailTableData.reduce((sum, item) => sum + (item.totalPaymentAmount - item.totalPurchasePrice), 0);
                return totalSales > 0 && (netProfit / totalSales * 100) >= 0 ? '#67C23A' : '#F56C6C';
              })(),
              fontWeight: 'bold'
            }"
          >
            {{ (() => {
              const totalSales = orderDetailTableData.reduce((sum, item) => sum + item.totalSales, 0);
              const netProfit = orderDetailTableData.reduce((sum, item) => sum + (item.totalPaymentAmount - item.totalPurchasePrice), 0);
              return totalSales > 0 ? (netProfit / totalSales * 100).toFixed(2) : '0.00';
            })() }}%
          </span>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.order-detail-table {
  width: 100%;
}

.table-card {
  height: 550px;
}

.table-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.3);
}

.table-card :deep(.el-card__body) {
  padding: 16px;
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.record-count {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.table-summary {
  display: flex;
  justify-content: center;
  gap: 32px;
  padding: 16px 0 8px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 16px;
  flex-shrink: 0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  font-size: 14px;
  font-weight: 500;
}

.summary-value {
  font-size: 16px;
  font-weight: bold;
}

.summary-value.income {
  color: #67C23A;
}

.summary-value.expenditure {
  color: #F56C6C;
}

/* 现代化表格样式 */
.table-container :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: hsl(var(--accent));
        color: hsl(var(--foreground));
        font-weight: 600;
        border-bottom: 2px solid hsl(var(--border));
        font-size: 14px;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        transition: all 0.2s ease;

        &:hover {
          background-color: hsl(var(--accent-hover));
          transform: translateY(-1px);
          box-shadow: 0 2px 8px hsl(var(--overlay-content));
        }

        &.el-table__row--selected {
          background-color: hsl(var(--accent));
        }
      }

      td {
        border-bottom: 1px solid hsl(var(--border));
        padding: 12px 0;
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: hsl(var(--foreground));
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-description {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}



/* 响应式设计 */
@media (max-width: 768px) {
  .table-card {
    height: 500px;
  }

  .card-title {
    font-size: 15px;
  }

  .record-count {
    font-size: 13px;
  }

  .table-card :deep(.el-card__header) {
    padding: 12px 16px;
  }

  .table-card :deep(.el-card__body) {
    padding: 12px;
  }

  .table-summary {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .summary-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-card {
    height: 450px;
  }

  .card-title {
    font-size: 14px;
  }

  .record-count {
    font-size: 12px;
  }

  .table-card :deep(.el-card__header) {
    padding: 10px 12px;
  }

  .table-card :deep(.el-card__body) {
    padding: 8px;
  }

  .card-header {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
}
</style>
