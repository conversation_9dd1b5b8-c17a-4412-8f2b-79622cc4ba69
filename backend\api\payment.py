from flask import Blueprint, request, g
from .auth import token_required, check_permission
from .utils import get_db_connection, format_response, validate_required_fields

payment_bp = Blueprint('payment', __name__, url_prefix='/api')

@payment_bp.route('/payments', methods=['GET'])
@token_required
@check_permission('MENU_PAYMENTRECEIPT')
def get_payments():
    """获取回款记录列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        date = request.args.get('date')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        shop = request.args.get('shop')

        # 构建查询条件
        where_conditions = ['is_deleted = 0']
        params = []

        # 日期条件
        if date:
            where_conditions.append('date = %s')
            params.append(date)
        elif start_date and end_date:
            where_conditions.append('date >= %s AND date <= %s')
            params.extend([start_date, end_date])

        # 店铺条件
        if shop:
            where_conditions.append('shop = %s')
            params.append(shop)
        
        where_clause = ' AND '.join(where_conditions)
        
        # 查询总数
        count_sql = f'SELECT COUNT(*) FROM paymentreceipt WHERE {where_clause}'
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]
        
        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT id, date, shop, amount
            FROM paymentreceipt
            WHERE {where_clause}
            ORDER BY id DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        
        payments = []
        for row in cursor.fetchall():
            payments.append({
                'id': row[0],
                'date': row[1],
                'shop': row[2],
                'amount': row[3]
            })
        
        return format_response(
            success=True,
            data=payments,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        print(f"获取回款列表错误: {str(e)}")
        return format_response(success=False, message="获取回款列表失败")
    finally:
        conn.close()

@payment_bp.route('/payments', methods=['POST'])
@token_required
@check_permission('PAYMENT_ADD')
def create_payment():
    """创建回款记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['date', 'shop', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入回款记录
        cursor.execute('''
            INSERT INTO paymentreceipt (date, shop, amount, is_deleted)
            VALUES (%s, %s, %s, %s)
        ''', (
            data['date'],
            data['shop'],
            data.get('amount', 0),
            0
        ))

        payment_id = cursor.lastrowid
        conn.commit()

        return format_response(
            success=True,
            data={'id': payment_id, **data},
            message="回款记录创建成功"
        )

    except Exception as e:
        print(f"创建回款记录错误: {str(e)}")
        return format_response(success=False, message="创建回款记录失败")
    finally:
        conn.close()

@payment_bp.route('/payments/<int:payment_id>', methods=['PUT'])
@token_required
@check_permission('PAYMENT_EDIT')
def update_payment(payment_id):
    """更新回款记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['date', 'shop', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款记录是否存在
        cursor.execute('SELECT COUNT(*) FROM paymentreceipt WHERE id = %s AND is_deleted = 0', (payment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款记录不存在")

        # 更新回款记录
        cursor.execute('''
            UPDATE paymentreceipt
            SET date = %s, shop = %s, amount = %s
            WHERE id = %s
        ''', (
            data['date'],
            data['shop'],
            data.get('amount', 0),
            payment_id
        ))

        conn.commit()

        return format_response(
            success=True,
            data={'id': payment_id, **data},
            message="回款记录更新成功"
        )

    except Exception as e:
        print(f"更新回款记录错误: {str(e)}")
        return format_response(success=False, message="更新回款记录失败")
    finally:
        conn.close()

@payment_bp.route('/payments/<int:payment_id>', methods=['DELETE'])
@token_required
@check_permission('PAYMENT_DELETE')
def delete_payment(payment_id):
    """删除回款记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款记录是否存在
        cursor.execute('SELECT COUNT(*) FROM paymentreceipt WHERE id = %s AND is_deleted = 0', (payment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款记录不存在")

        # 软删除回款记录
        cursor.execute('UPDATE paymentreceipt SET is_deleted = 1 WHERE id = %s', (payment_id,))
        conn.commit()

        return format_response(success=True, message="回款记录删除成功")

    except Exception as e:
        print(f"删除回款记录错误: {str(e)}")
        return format_response(success=False, message="删除回款记录失败")
    finally:
        conn.close()

@payment_bp.route('/payments/batch-delete', methods=['POST'])
@token_required
@check_permission('PAYMENT_BATCH_DELETE')
def batch_delete_payments():
    """批量删除回款记录"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要删除的回款记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量软删除
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'UPDATE paymentreceipt SET is_deleted = 1 WHERE id IN ({placeholders})', ids)
        
        affected_rows = cursor.rowcount
        conn.commit()

        return format_response(success=True, message=f"批量删除成功，共删除{affected_rows}条记录")

    except Exception as e:
        print(f"批量删除回款记录错误: {str(e)}")
        return format_response(success=False, message="批量删除回款记录失败")
    finally:
        conn.close()
