import { requestClient } from '#/api/request';

// 订单搜索参数接口
export interface OrderSearchParams {
  page?: number;
  pageSize?: number;
  date?: string;
  start_date?: string;
  end_date?: string;
  shop?: string;
  order_no?: string;
}

// 订单数据接口
export interface Order {
  id?: number;
  date: string;
  shop: string;
  order_no: string;
  sale_price: number;
  box_fee?: number;  // 保持与后端一致
  delivery_fee?: number;
  actual_payment: number;
  purchase_price?: number;
  product_profit?: number;
  total_profit?: number;
  screenshot?: string;
}

/**
 * 获取订单列表
 */
export async function fetchOrderList(params: OrderSearchParams) {
  return requestClient.get<{
    data: Order[];
    total: number;
    page: number;
    pageSize: number;
  }>('/orders', {
    params,
    responseReturn: 'body',
  });
}

/**
 * 添加订单
 */
export async function addOrder(data: Order) {
  return requestClient.post<{ data: Order }>('/orders', data);
}

/**
 * 更新订单
 */
export async function updateOrder(id: number, data: Order) {
  return requestClient.put<{ data: Order }>(`/orders/${id}`, data);
}

/**
 * 删除订单
 */
export async function deleteOrder(id: number) {
  return requestClient.delete(`/orders/${id}`);
}

/**
 * 批量删除订单
 */
export async function batchDeleteOrder(ids: number[]) {
  return requestClient.post('/orders/batch-delete', { ids });
}

/**
 * 获取线上店铺列表（用于下拉选择）
 */
export async function fetchOnlineStoreOptions() {
  return requestClient.get<{
    data: Array<{ id: number; name: string; status: number }>;
  }>('/online-stores');
}

// 日历数据接口
export interface CalendarDayData {
  date: string;
  orderCount: number;
  totalSales: number;
  netProfit: number;
}

export interface CalendarData {
  [shop: string]: CalendarDayData[];
}

/**
 * 获取日历数据
 */
export async function fetchCalendarData(params: {
  start_date: string;
  end_date: string;
  shops?: string[];
}) {
  const requestParams: any = {
    start_date: params.start_date,
    end_date: params.end_date,
  };

  if (params.shops && params.shops.length > 0) {
    requestParams.shops = params.shops;
  }

  return requestClient.get<{
    data: CalendarData;
  }>('/orders/calendar', {
    params: requestParams,
    responseReturn: 'body',
  });
}
