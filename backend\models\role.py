import psycopg2
import psycopg2.extras
from datetime import datetime
from config.config import Config

class Role:
    """角色模型类"""
    
    def __init__(self):
        self.db_config = Config.DATABASE_CONFIG
    
    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(**self.db_config)
        return conn
    
    def get_roles_list(self, page=1, page_size=10, name=None, status=None):
        """获取角色列表（分页）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # 构建查询条件
        where_conditions = ['r.is_deleted = 0']
        params = []
        
        if name:
            where_conditions.append('r.name LIKE %s')
            params.append(f'%{name}%')

        if status is not None:
            where_conditions.append('r.status = %s')
            params.append(status)
        
        where_clause = ' AND '.join(where_conditions)
        
        # 获取总数
        count_sql = f'SELECT COUNT(*) FROM roles r WHERE {where_clause}'
        cursor.execute(count_sql, params)
        total = cursor.fetchone()['count']
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT r.id, r.name, r.description, r.status, r.created_at, r.updated_at,
                   COUNT(DISTINCT rp.permission_id) as permission_count,
                   COUNT(DISTINCT CASE WHEN u.is_deleted = 0 THEN ur.user_id END) as user_count
            FROM roles r
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN user_roles ur ON r.id = ur.role_id
            LEFT JOIN users u ON ur.user_id = u.id
            WHERE {where_clause}
            GROUP BY r.id, r.name, r.description, r.status, r.created_at, r.updated_at
            ORDER BY r.created_at DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        
        roles = cursor.fetchall()
        conn.close()
        
        return {
            'roles': [dict(role) for role in roles],
            'total': total,
            'page': page,
            'page_size': page_size
        }
    
    def create_role(self, name, description=None, status=1):
        """创建角色"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查角色名是否已存在
            cursor.execute('SELECT id FROM roles WHERE name = %s AND is_deleted = 0', (name,))
            if cursor.fetchone():
                return {'success': False, 'message': '角色名已存在'}
            
            # 插入角色
            cursor.execute('''
                INSERT INTO roles (name, description, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id
            ''', (name, description, status, datetime.now(), datetime.now()))

            role_id = cursor.fetchone()[0]
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': '角色创建成功', 'role_id': role_id}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'创建角色失败: {str(e)}'}

    def update_role(self, role_id, name=None, description=None, status=None):
        """更新角色信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 检查角色是否存在
            cursor.execute('SELECT id FROM roles WHERE id = %s AND is_deleted = 0', (role_id,))
            if not cursor.fetchone():
                return {'success': False, 'message': '角色不存在'}
            
            update_fields = []
            params = []
            
            if name is not None:
                # 检查新角色名是否已存在
                cursor.execute('SELECT id FROM roles WHERE name = %s AND id != %s AND is_deleted = 0', (name, role_id))
                if cursor.fetchone():
                    return {'success': False, 'message': '角色名已存在'}
                update_fields.append('name = %s')
                params.append(name)
            
            if description is not None:
                update_fields.append('description = %s')
                params.append(description)
            
            if status is not None:
                update_fields.append('status = %s')
                params.append(status)
            
            if not update_fields:
                return {'success': False, 'message': '没有需要更新的字段'}
            
            update_fields.append('updated_at = %s')
            params.append(datetime.now())
            params.append(role_id)
            
            update_sql = f'UPDATE roles SET {", ".join(update_fields)} WHERE id = %s'
            cursor.execute(update_sql, params)
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': '角色更新成功'}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'更新角色失败: {str(e)}'}

    def delete_role(self, role_id):
        """删除角色（软删除）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        try:
            # 检查角色是否存在
            cursor.execute('SELECT id, name FROM roles WHERE id = %s AND is_deleted = 0', (role_id,))
            role = cursor.fetchone()
            if not role:
                return {'success': False, 'message': '角色不存在'}
            
            # 检查是否有用户使用此角色（只统计未删除的用户）
            cursor.execute('''
                SELECT COUNT(*) FROM user_roles ur
                JOIN users u ON ur.user_id = u.id
                WHERE ur.role_id = %s AND u.is_deleted = 0
            ''', (role_id,))
            user_count = cursor.fetchone()['count']
            if user_count > 0:
                return {'success': False, 'message': f'该角色正在被 {user_count} 个用户使用，无法删除'}
            
            # 软删除角色
            cursor.execute('''
                UPDATE roles SET is_deleted = 1, updated_at = %s WHERE id = %s
            ''', (datetime.now(), role_id))
            
            # 删除角色权限关联
            cursor.execute('DELETE FROM role_permissions WHERE role_id = %s', (role_id,))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': '角色删除成功'}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'删除角色失败: {str(e)}'}

    def batch_delete_roles(self, role_ids):
        """批量删除角色（软删除）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        try:
            success_count = 0
            failed_roles = []
            
            for role_id in role_ids:
                # 检查角色是否存在
                cursor.execute('SELECT id, name FROM roles WHERE id = %s AND is_deleted = 0', (role_id,))
                role = cursor.fetchone()
                if not role:
                    failed_roles.append(f'角色ID {role_id} 不存在')
                    continue
                
                # 检查是否有用户使用此角色（只统计未删除的用户）
                cursor.execute('''
                    SELECT COUNT(*) FROM user_roles ur
                    JOIN users u ON ur.user_id = u.id
                    WHERE ur.role_id = %s AND u.is_deleted = 0
                ''', (role_id,))
                user_count = cursor.fetchone()['count']
                if user_count > 0:
                    failed_roles.append(f'角色 {role["name"]} 正在被使用，无法删除')
                    continue
                
                # 软删除角色
                cursor.execute('''
                    UPDATE roles SET is_deleted = 1, updated_at = %s WHERE id = %s
                ''', (datetime.now(), role_id))
                
                # 删除角色权限关联
                cursor.execute('DELETE FROM role_permissions WHERE role_id = %s', (role_id,))
                
                success_count += 1
            
            conn.commit()
            conn.close()
            
            if failed_roles:
                return {
                    'success': True, 
                    'message': f'成功删除 {success_count} 个角色，{len(failed_roles)} 个失败',
                    'failed_details': failed_roles
                }
            else:
                return {'success': True, 'message': f'成功删除 {success_count} 个角色'}
                
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'message': f'批量删除角色失败: {str(e)}'}
    
    def get_role_by_id(self, role_id):
        """根据ID获取角色信息"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT id, name, description, status, created_at, updated_at
            FROM roles
            WHERE id = %s AND is_deleted = 0
        ''', (role_id,))
        
        role = cursor.fetchone()
        conn.close()
        
        if role:
            return dict(role)
        return None

    def get_all_roles(self):
        """获取所有角色（不分页）"""
        conn = self.get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cursor.execute('''
            SELECT id, name, description, status, created_at, updated_at
            FROM roles
            WHERE is_deleted = 0 AND status = 1
            ORDER BY id
        ''')
        
        roles = cursor.fetchall()
        conn.close()
        
        return [dict(role) for role in roles]
